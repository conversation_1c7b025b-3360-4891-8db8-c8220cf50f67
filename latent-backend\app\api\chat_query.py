from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional, Dict
import logging
import os
import jwt
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()


from app.services.chatbot_services.azure_search_service import AzureSearchService
from app.services.chatbot_services.openai_service import OpenAIService
from app.models.chatbot_models import QueryRequest, QueryResponse, DocumentChunk, ChatTurn
from app.services.error_logger import log_session_request, log_interaction
import uuid

logger = logging.getLogger(__name__)  # (logger currently diabled)
logging.disable(logging.CRITICAL)

# Security scheme for optional JWT
security = HTTPBearer(auto_error=False)

def get_client_ip(request: Request) -> str:
    """Extract client IP address from request"""
    # Check for forwarded headers first (for reverse proxy setups)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fallback to direct client IP
    return request.client.host if request.client else "unknown"

def extract_user_info_from_token(credentials: Optional[HTTPAuthorizationCredentials]) -> tuple[str, str]:
    """Extract user info from JWT token if available"""
    if not credentials:
        return "unknown", "unknown"

    try:
        # Decode token without verification (for development)
        payload = jwt.decode(credentials.credentials, options={"verify_signature": False})
        user_id = payload.get("sub", "unknown")
        user_email = payload.get("email", "unknown")
        return user_id, user_email
    except Exception as e:
        logger.warning(f"Could not decode token: {e}")
        return "unknown", "unknown"

class QueryController:
    def __init__(self):
        self.search_service = AzureSearchService()
        self.openai_service = OpenAIService()
    
    async def process_query(self, request: QueryRequest, session_id: str) -> QueryResponse:
        try:

            logger.info(f"Processing query: {request.query}")

            # Log interaction: Start processing query
            await log_interaction(session_id, "1. start query processing", f"Processing query: {request.query[:100]}...",
                                 params={"query_length": len(request.query), "filter_titles_count": len(request.filter_titles) if request.filter_titles else 0})

            # Step 1: Generate embeddings
            logger.info("Step 1: Generating query embeddings...")
            await log_interaction(session_id, "2. generate embeddings", "Generating query embeddings")
            query_vector = await self.openai_service.get_embeddings(request.query)
            logger.info(f"Query embeddings generated successfully: {query_vector[:5]}... (total {len(query_vector)})")
            await log_interaction(session_id, "3. embeddings generated", f"Query embeddings generated successfully",
                                 response={"embedding_length": len(query_vector)})
            
            user_id = request.user_id if request.user_id is not None else ""
            titles = request.filter_titles if request.filter_titles is not None else []
            
            # Step 2: Search documents
            logger.info("Step 2: Searching for relevant documents...")
            logger.info(f"Search parameters: user_id={user_id}, top_k=3, titles_filter_count={len(titles)}")
            await log_interaction(session_id, "4. search documents", "Searching for relevant documents",
                                 params={"user_id": user_id, "top_k": 4, "titles_filter_count": len(titles)})
            documents = await self.search_service.search_documents(
                query_vector=query_vector,
                user_id=user_id,
                top_k=4,
                titles=titles,
                blob_to_title_mapping=request.blob_to_title_mapping
            )
            logger.info(f"Document search completed. Found {len(documents) if documents else 0} documents")
            await log_interaction(session_id, "5. documents found", f"Document search completed",
                                 response={"documents_count": len(documents) if documents else 0})
            
            if not documents:
                logger.warning("No relevant documents found. Returning fallback response.")
                await log_interaction(session_id, "6. no documents found", "No relevant documents found, returning fallback response", status="warning")
                return QueryResponse(
                    answer="I couldn't find any relevant documents to answer your question.",
                    sources=[],
                    query=request.query,
                    total_sources=0
                )
            
            # Step 3: Prepare context from documents
            logger.info("Step 3: Preparing context from retrieved documents...")
            
            context = "\n\n".join([
                f"Document: {doc.doc_name}\nContent: {doc.content}" for doc in documents
            ])
            logger.info(f"Combined context preview: {context[:300]}...")
            
            # Step 4: Generate response using OpenAI
            logger.info("Step 4: Generating AI response...")
            chat_history_log = ""
            if request.chat_history and len(request.chat_history) > 0:
                logger.info(f"Including chat history with {len(request.chat_history)} previous turns")
                for i, turn in enumerate(request.chat_history):
                    chat_history_log += f"Turn {i+1}: User: {turn.user_query[:50]}...\n"
                    chat_history_log += f"Turn {i+1}: Bot: {turn.bot_answer[:50]}...\n"
                logger.info(f"Chat history summary:\n{chat_history_log}")

            await log_interaction(session_id, "7. generate llm response", "Generating AI response using LLM",
                                 params={"context_length": len(context), "chat_history_turns": len(request.chat_history) if request.chat_history else 0})
            answer = await self.openai_service.generate_response(
                context,
                request.query,
                chat_history=request.chat_history
            )
            logger.info(f"LLM response generated successfully: {answer[:200]}...")
            await log_interaction(session_id, "8. llm response generated", "LLM response generated successfully",
                                 response={"response_length": len(answer), "response_preview": answer[:200]})
            
            # Step 5: Prepare and return response
            logger.info("Step 5: Preparing final response...")
            response = QueryResponse(
                answer=answer,
                sources=documents,
                query=request.query,
                total_sources=len(documents)
            )
            logger.info(f"Response prepared successfully with {len(documents)} sources")
            await log_interaction(session_id, "9. query completed", "Query processing completed successfully",
                                 response={"total_sources": len(documents), "answer_length": len(answer)})
            return response

        except Exception as e:
            logger.error(f"========== ERROR IN QUERY PROCESSING ==========")
            await log_interaction(session_id, "ERROR. query processing error", f"Error in query processing: {str(e)}", status="error",
                                 response={"error": str(e)})
            raise HTTPException(status_code=500, detail=f"Failed to process query: {str(e)}")

query_controller = QueryController()

# --- FastAPI Router ---
router = APIRouter()

@router.post("/query", response_model=QueryResponse)
async def process_query(
    request: QueryRequest,
    http_request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    """Process user query and return AI response with sources"""
    request_time = datetime.now().isoformat()

    # Extract user information from token if available
    token_user_id, user_email = extract_user_info_from_token(credentials)

    # Use user_id from request body if provided, otherwise use token user_id
    user_id = request.user_id if request.user_id else token_user_id

    # Extract client IP address
    ip_address = get_client_ip(http_request)

    # Generate session_id if not provided
    session_id = request.session_id if request.session_id else str(uuid.uuid4())

    logger.info(f"========== API REQUEST [{request_time}] [User: {user_id}] [Session: {session_id}] [IP: {ip_address}] ==========")
    logger.info(f"Request query: {request.query}")
    logger.info(f"Filters applied: {len(request.filter_titles) if request.filter_titles else 0} title filters")

    try:
        # Initialize session-based logging (creates record if new session, reuses if existing)
        valid_session_id = await log_session_request(
            session_id=session_id,
            user_id=user_id,
            user_email=user_email,
            ip_address=ip_address,
            endpoint="/chat/query",
            method="POST",
            request_params={
                "query": request.query[:100] + "..." if len(request.query) > 100 else request.query,
                "filter_titles_count": len(request.filter_titles) if request.filter_titles else 0,
                "chat_history_turns": len(request.chat_history) if request.chat_history else 0
            }
        )

        # Use the valid session ID for logging (will be None if session creation failed)
        logging_session_id = valid_session_id if valid_session_id else None

        logger.info(f"Delegating to QueryController.process_query()")
        response = await query_controller.process_query(request, logging_session_id)
        logger.info(f"Response status: Success")
        logger.info(f"Response size: {len(response.answer)} characters")
        return response
    except Exception as e:
        logger.error(f"Error details: {str(e)}", exc_info=True)
        await log_interaction(logging_session_id, "ERROR. api error", f"API error: {str(e)}", status="error", response={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Failed to process query: {str(e)}")