from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime

class ChatTurn(BaseModel):
    user_query: str
    bot_answer: str

class QueryRequest(BaseModel):
    query: str
    user_id: Optional[str] = None
    filter_titles: Optional[List[str]] = None
    chat_history: Optional[List[ChatTurn]] = None
    blob_to_title_mapping: Optional[Dict[str, str]] = None
    session_id: Optional[str] = None

class DocumentChunk(BaseModel):
    id: str
    content: str
    doc_id: str
    doc_name: str
    timestamp: Optional[str] = None
    score: Optional[float] = None
    metadata: Optional[dict] = None

class QueryResponse(BaseModel):
    answer: str
    sources: List[DocumentChunk]
    query: str
    total_sources: int