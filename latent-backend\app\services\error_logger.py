import datetime
import httpx
import os
from dotenv import load_dotenv

# Load environment variables - try multiple locations
try:
    # Try loading from current directory first
    load_dotenv()
    # If that doesn't work, try parent directories
    if not os.getenv("SUPABASE_URL"):
        load_dotenv(os.path.join(os.path.dirname(__file__), '..', '..', '.env'))
except ImportError:
    # If dotenv is not available, continue without it
    pass

def get_supabase_config():
    """Get Supabase configuration with fallback handling"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_PUBLIC_KEY")

    if not url or not key:
        print("Warning: SUPABASE_URL or SUPABASE_PUBLIC_KEY not found in environment variables")
        return None, None

    return url, key

# Log the start of a user request
async def log_user_request(user_id, user_email, ip_address, endpoint, method, request_params, request_id):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_user_request: Supabase configuration not available")
        return request_id
    data = {
        "id": request_id,
        "user_id": user_id,
        "user_email": user_email,
        "ip_address": ip_address,
        "endpoint": endpoint,
        "method": method,
        "request_params": request_params,
        "created_at": datetime.datetime.utcnow().isoformat()
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )
            if response.status_code >= 400:
                print(f"log_user_request failed: {response.status_code} {response.text}")
    except Exception as e:
        print(f"Exception in log_user_request: {e}")
    return request_id

# Session-based logging: Check if session exists, create if not, return request_id for interactions
async def log_session_request(session_id, user_id, user_email, ip_address, endpoint, method, request_params):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_session_request: Supabase configuration not available")
        return session_id

    try:
        # First, check if session already exists
        async with httpx.AsyncClient() as client:
            check_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                params={"id": f"eq.{session_id}"}
            )

            if check_response.status_code == 200:
                existing_records = check_response.json()
                if existing_records:
                    # Session exists, return the session_id
                    print(f"Session {session_id} already exists, reusing for logging")
                    return session_id

            # Session doesn't exist, create new record with session_id as the id
            data = {
                "id": session_id,
                "user_id": user_id,
                "user_email": user_email,
                "ip_address": ip_address,
                "endpoint": endpoint,
                "method": method,
                "request_params": request_params,
                "created_at": datetime.datetime.utcnow().isoformat()
            }

            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )

            if response.status_code >= 400:
                print(f"log_session_request failed: {response.status_code} {response.text}")
            else:
                print(f"New session {session_id} created for logging")

    except Exception as e:
        print(f"Exception in log_session_request: {e}")

    return session_id

# Log an interaction step
async def log_interaction(request_id, step, description, params=None, response=None, status="success"):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_interaction: Supabase configuration not available")
        return
    data = {
        "request_id": request_id,
        "step": step,
        "description": description,
        "params": params,
        "response": response,
        "status": status,
        "created_at": datetime.datetime.utcnow().isoformat()
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/request_interactions",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )
            if response.status_code >= 400:
                print(f"log_interaction failed: {response.status_code} {response.text}")
    except Exception as e:
        print(f"Exception in log_interaction: {e}")

# Synchronous version for use in sync contexts
def log_interaction_sync(request_id, step, description, params=None, response=None, status="success"):
    """Synchronous version of log_interaction for use in sync contexts"""
    import asyncio
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, create a task
            asyncio.create_task(log_interaction(request_id, step, description, params, response, status))
        else:
            # If no loop is running, run the async function
            asyncio.run(log_interaction(request_id, step, description, params, response, status))
    except RuntimeError:
        # If no event loop exists, run the async function
        try:
            asyncio.run(log_interaction(request_id, step, description, params, response, status))
        except Exception as e:
            print(f"Failed to log interaction synchronously: {e}")