import datetime
import httpx
import os
from dotenv import load_dotenv

# Load environment variables - try multiple locations
try:
    # Try loading from current directory first
    load_dotenv()
    # If that doesn't work, try parent directories
    if not os.getenv("SUPABASE_URL"):
        load_dotenv(os.path.join(os.path.dirname(__file__), '..', '..', '.env'))
except ImportError:
    # If dotenv is not available, continue without it
    pass

def get_supabase_config():
    """Get Supabase configuration with fallback handling"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_PUBLIC_KEY")

    if not url or not key:
        print("Warning: SUPABASE_URL or SUPABASE_PUBLIC_KEY not found in environment variables")
        return None, None

    return url, key

# Log the start of a user request
async def log_user_request(user_id, user_email, ip_address, endpoint, method, request_params, request_id):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_user_request: Supabase configuration not available")
        return request_id
    data = {
        "id": request_id,
        "user_id": user_id,
        "user_email": user_email,
        "ip_address": ip_address,
        "endpoint": endpoint,
        "method": method,
        "request_params": request_params,
        "created_at": datetime.datetime.utcnow().isoformat()
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )
            if response.status_code >= 400:
                print(f"log_user_request failed: {response.status_code} {response.text}")
    except Exception as e:
        print(f"Exception in log_user_request: {e}")
    return request_id

# Session-based logging: Check if session exists, create if not, return request_id for interactions
async def log_session_request(session_id, user_id, user_email, ip_address, endpoint, method, request_params):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_session_request: Supabase configuration not available")
        return session_id

    try:
        # First, check if session already exists
        async with httpx.AsyncClient() as client:
            print(f"Checking if session {session_id} exists...")
            check_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                params={"id": f"eq.{session_id}"}
            )

            print(f"Session check response: {check_response.status_code}")
            if check_response.status_code == 200:
                existing_records = check_response.json()
                print(f"Existing records found: {len(existing_records) if existing_records else 0}")
                if existing_records and len(existing_records) > 0:
                    # Session exists, return the session_id
                    print(f"Session {session_id} already exists, reusing for logging")
                    return session_id
            else:
                print(f"Session check failed: {check_response.status_code} {check_response.text}")

            # Session doesn't exist, create new record with session_id as the id
            print(f"Creating new session {session_id}...")
            data = {
                "id": session_id,
                "user_id": user_id,
                "user_email": user_email,
                "ip_address": ip_address,
                "endpoint": endpoint,
                "method": method,
                "request_params": request_params,
                "created_at": datetime.datetime.utcnow().isoformat()
            }

            print(f"Session data: {data}")
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/user_requests",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )
            print(f"Session creation response: {response.status_code}")

            if response.status_code >= 400:
                print(f"log_session_request failed: {response.status_code} {response.text}")
                # Try to get more details about the error
                try:
                    error_details = response.json()
                    print(f"Error details: {error_details}")
                except:
                    pass
                # Return None to indicate session creation failed
                return None
            else:
                print(f"New session {session_id} created for logging")

                # Verify the session was actually created by checking again
                verify_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/user_requests",
                    headers={
                        "apikey": SUPABASE_PUBLIC_KEY,
                        "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                        "Content-Type": "application/json"
                    },
                    params={"id": f"eq.{session_id}"}
                )

                if verify_response.status_code == 200:
                    verify_records = verify_response.json()
                    if verify_records and len(verify_records) > 0:
                        print(f"Session {session_id} verified in database")
                        return session_id
                    else:
                        print(f"Session {session_id} not found after creation - verification failed")
                        return None
                else:
                    print(f"Session verification failed: {verify_response.status_code}")
                    return None

    except Exception as e:
        print(f"Exception in log_session_request: {e}")
        # Return None to indicate session creation failed
        return None

# Log an interaction step
async def log_interaction(request_id, step, description, params=None, response=None, status="success"):
    SUPABASE_URL, SUPABASE_PUBLIC_KEY = get_supabase_config()

    # Skip logging if configuration is not available
    if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
        print("Skipping log_interaction: Supabase configuration not available")
        return

    # Skip logging if request_id is None (session creation failed)
    if not request_id:
        print(f"Skipping log_interaction '{step}': No valid session ID")
        return

    print(f"Logging interaction '{step}' for session {request_id}")
    data = {
        "request_id": request_id,
        "step": step,
        "description": description,
        "params": params,
        "response": response,
        "status": status,
        "created_at": datetime.datetime.utcnow().isoformat()
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/request_interactions",
                headers={
                    "apikey": SUPABASE_PUBLIC_KEY,
                    "Authorization": f"Bearer {SUPABASE_PUBLIC_KEY}",
                    "Content-Type": "application/json"
                },
                json=data
            )
            if response.status_code >= 400:
                print(f"log_interaction failed: {response.status_code} {response.text}")
                # Try to get more details about the error
                try:
                    error_details = response.json()
                    print(f"Interaction error details: {error_details}")

                    # If it's a foreign key constraint error, the session doesn't exist
                    if "23503" in str(error_details.get("code", "")):
                        print(f"Foreign key constraint error - session {request_id} doesn't exist in user_requests table")
                except:
                    pass
    except Exception as e:
        print(f"Exception in log_interaction: {e}")

# Synchronous version for use in sync contexts
def log_interaction_sync(request_id, step, description, params=None, response=None, status="success"):
    """Synchronous version of log_interaction for use in sync contexts"""
    import asyncio
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, create a task
            asyncio.create_task(log_interaction(request_id, step, description, params, response, status))
        else:
            # If no loop is running, run the async function
            asyncio.run(log_interaction(request_id, step, description, params, response, status))
    except RuntimeError:
        # If no event loop exists, run the async function
        try:
            asyncio.run(log_interaction(request_id, step, description, params, response, status))
        except Exception as e:
            print(f"Failed to log interaction synchronously: {e}")