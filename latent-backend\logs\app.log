2025-07-10 17:31:16,904 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-10 17:31:17,278 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-10 17:31:27,156 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-10 17:31:27,562 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-10 17:32:30,120 - app - INFO - ========== API REQUEST [2025-07-10T17:32:30.120471] [User: unknown] ==========
2025-07-10 17:32:30,122 - app - INFO - ========== API REQUEST [2025-07-10T17:32:30.120471] ==========
2025-07-10 17:32:30,123 - app - INFO - Request query: what field of work this SOP is suitable for?
2025-07-10 17:32:30,125 - app - INFO - Filters applied: 1 title filters
2025-07-10 17:32:30,126 - app - INFO - Delegating to QueryController.process_query()
2025-07-10 17:32:30,128 - app - INFO - Processing query: what field of work this SOP is suitable for?
2025-07-10 17:32:30,139 - app - INFO - Step 1: Generating query embeddings...
2025-07-10 17:32:33,508 - app.services.chatbot_services.openai_service - INFO - Generated embedding of length: 3072
2025-07-10 17:32:33,511 - app - INFO - Query embeddings generated successfully: [-0.0026451051235198975, -0.027905134484171867, -0.025170961394906044, -0.006127032916992903, 0.018045540899038315]... (total 3072)
2025-07-10 17:32:33,512 - app - INFO - Step 2: Searching for relevant documents...
2025-07-10 17:32:33,514 - app - INFO - Search parameters: user_id=, top_k=3, titles_filter_count=1
2025-07-10 17:32:33,518 - app.services.chatbot_services.azure_search_service - INFO - Applying titles filter: title eq 'fae17448-8d0a-4992-9c8d-9245507529d1-SOP for Failure Investigation.pdf'
2025-07-10 17:32:35,949 - app - INFO - Document search completed. Found 4 documents
2025-07-10 17:32:35,951 - app - INFO - Step 3: Preparing context from retrieved documents...
2025-07-10 17:32:35,952 - app - INFO - Combined context preview: Document: Failure Investigation
Content: SOP for Failure Investigation

Standard operating procedure to investigate the failure of batch in pharmaceutical manufacturing through the investigation of analytical data, cleaning,

equipment used, equipment qualification and trend data of the product.

1....
2025-07-10 17:32:35,954 - app - INFO - Step 4: Generating AI response...
2025-07-10 17:32:38,531 - app - INFO - LLM response generated successfully: The Standard Operating Procedure (SOP) for Failure Investigation outlined in the provided document is suitable for the pharmaceutical manufacturing field. It specifically focuses on investigating prod...
2025-07-10 17:32:38,532 - app - INFO - Step 5: Preparing final response...
2025-07-10 17:32:38,534 - app - INFO - Response prepared successfully with 4 sources
2025-07-10 17:32:38,536 - app - INFO - Response status: Success
2025-07-10 17:32:38,537 - app - INFO - Response size: 853 characters
2025-07-10 17:32:59,743 - app - INFO - ========== API REQUEST [2025-07-10T17:32:59.743743] [User: unknown] ==========
2025-07-10 17:32:59,745 - app - INFO - ========== API REQUEST [2025-07-10T17:32:59.743743] ==========
2025-07-10 17:32:59,747 - app - INFO - Request query: can you name some suitable regulations to run on?
2025-07-10 17:32:59,749 - app - INFO - Filters applied: 1 title filters
2025-07-10 17:32:59,751 - app - INFO - Delegating to QueryController.process_query()
2025-07-10 17:32:59,753 - app - INFO - Processing query: can you name some suitable regulations to run on?
2025-07-10 17:32:59,754 - app - INFO - Step 1: Generating query embeddings...
2025-07-10 17:33:02,074 - app.services.chatbot_services.openai_service - INFO - Generated embedding of length: 3072
2025-07-10 17:33:02,075 - app - INFO - Query embeddings generated successfully: [-0.010243122465908527, 0.002827330259606242, -0.023837154731154442, 0.003624599426984787, 0.0030843603890389204]... (total 3072)
2025-07-10 17:33:02,077 - app - INFO - Step 2: Searching for relevant documents...
2025-07-10 17:33:02,079 - app - INFO - Search parameters: user_id=, top_k=3, titles_filter_count=1
2025-07-10 17:33:02,081 - app.services.chatbot_services.azure_search_service - INFO - Applying titles filter: title eq 'fae17448-8d0a-4992-9c8d-9245507529d1-SOP for Failure Investigation.pdf'
2025-07-10 17:33:05,557 - app - INFO - Document search completed. Found 4 documents
2025-07-10 17:33:05,559 - app - INFO - Step 3: Preparing context from retrieved documents...
2025-07-10 17:33:05,560 - app - INFO - Combined context preview: Document: Failure Investigation
Content: verify the implementation of corrective and

preventive actions taken.

5.20 The failure investigation shall be closed by Head QA after verification of the implementation of CAPA.

6.0 ABBREVIATIONS

6.1 SOP: Standard Operating Procedure

6.2 QA: Quality Assu...
2025-07-10 17:33:05,562 - app - INFO - Step 4: Generating AI response...
2025-07-10 17:33:05,563 - app - INFO - Including chat history with 1 previous turns
2025-07-10 17:33:05,565 - app - INFO - Chat history summary:
Turn 1: User: what field of work this SOP is suitable for?...
Turn 1: Bot: The Standard Operating Procedure (SOP) for Failure...

2025-07-10 17:33:07,276 - app - INFO - LLM response generated successfully: Based on the provided document context, some suitable regulations to run on for the Failure Investigation SOP in pharmaceutical manufacturing could include Good Manufacturing Practice (GMP) regulation...
2025-07-10 17:33:07,278 - app - INFO - Step 5: Preparing final response...
2025-07-10 17:33:07,280 - app - INFO - Response prepared successfully with 4 sources
2025-07-10 17:33:07,281 - app - INFO - Response status: Success
2025-07-10 17:33:07,283 - app - INFO - Response size: 422 characters
2025-07-10 17:35:14,184 - app - INFO - ========== API REQUEST [2025-07-10T17:35:14.184631] [User: unknown] ==========
2025-07-10 17:35:14,186 - app - INFO - ========== API REQUEST [2025-07-10T17:35:14.184631] ==========
2025-07-10 17:35:14,188 - app - INFO - Request query: what regulation talks about inspection steps?
2025-07-10 17:35:14,190 - app - INFO - Filters applied: 1 title filters
2025-07-10 17:35:14,192 - app - INFO - Delegating to QueryController.process_query()
2025-07-10 17:35:14,193 - app - INFO - Processing query: what regulation talks about inspection steps?
2025-07-10 17:35:14,195 - app - INFO - Step 1: Generating query embeddings...
2025-07-10 17:35:16,650 - app.services.chatbot_services.openai_service - INFO - Generated embedding of length: 3072
2025-07-10 17:35:16,652 - app - INFO - Query embeddings generated successfully: [-0.01509153377264738, 0.01267403457313776, -0.01681945100426674, 0.024064021185040474, -0.00802134070545435]... (total 3072)
2025-07-10 17:35:16,654 - app - INFO - Step 2: Searching for relevant documents...
2025-07-10 17:35:16,656 - app - INFO - Search parameters: user_id=, top_k=3, titles_filter_count=1
2025-07-10 17:35:16,658 - app.services.chatbot_services.azure_search_service - INFO - Applying titles filter: title eq 'fae17448-8d0a-4992-9c8d-9245507529d1-SOP for Failure Investigation.pdf'
2025-07-10 17:35:18,899 - app - INFO - Document search completed. Found 4 documents
2025-07-10 17:35:18,900 - app - INFO - Step 3: Preparing context from retrieved documents...
2025-07-10 17:35:18,902 - app - INFO - Combined context preview: Document: Failure Investigation
Content: verify the implementation of corrective and

preventive actions taken.

5.20 The failure investigation shall be closed by Head QA after verification of the implementation of CAPA.

6.0 ABBREVIATIONS

6.1 SOP: Standard Operating Procedure

6.2 QA: Quality Assu...
2025-07-10 17:35:18,904 - app - INFO - Step 4: Generating AI response...
2025-07-10 17:35:18,905 - app - INFO - Including chat history with 2 previous turns
2025-07-10 17:35:18,906 - app - INFO - Chat history summary:
Turn 1: User: what field of work this SOP is suitable for?...
Turn 1: Bot: The Standard Operating Procedure (SOP) for Failure...
Turn 2: User: can you name some suitable regulations to run on?...
Turn 2: Bot: Based on the provided document context, some suita...

2025-07-10 17:35:20,861 - app - INFO - LLM response generated successfully: The provided document context does not contain specific information about regulations that talk about inspection steps....
2025-07-10 17:35:20,862 - app - INFO - Step 5: Preparing final response...
2025-07-10 17:35:20,864 - app - INFO - Response prepared successfully with 4 sources
2025-07-10 17:35:20,866 - app - INFO - Response status: Success
2025-07-10 17:35:20,867 - app - INFO - Response size: 119 characters
2025-07-10 17:35:50,425 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-10 17:35:50,881 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-10 17:36:00,581 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-10 17:36:00,986 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-10 17:36:06,939 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-10 17:36:07,333 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-11 08:27:47,704 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-11 08:27:47,945 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-11 08:27:50,346 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-11 08:27:50,582 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-11 08:27:57,721 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-11 08:27:57,946 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-11 08:28:21,990 - app - INFO - ========== API REQUEST [2025-07-11T08:28:21.990142] [User: unknown] ==========
2025-07-11 08:28:21,991 - app - INFO - ========== API REQUEST [2025-07-11T08:28:21.990142] ==========
2025-07-11 08:28:21,993 - app - INFO - Request query: what all sops we covering on this tool?
2025-07-11 08:28:21,995 - app - INFO - Filters applied: 25 title filters
2025-07-11 08:28:21,996 - app - INFO - Delegating to QueryController.process_query()
2025-07-11 08:28:21,998 - app - INFO - Processing query: what all sops we covering on this tool?
2025-07-11 08:28:21,999 - app - INFO - Step 1: Generating query embeddings...
2025-07-11 08:28:25,416 - app.services.chatbot_services.openai_service - INFO - Generated embedding of length: 3072
2025-07-11 08:28:25,418 - app - INFO - Query embeddings generated successfully: [-0.023330053314566612, -0.01672307960689068, -0.024835217744112015, -0.015410436317324638, -0.014325316995382309]... (total 3072)
2025-07-11 08:28:25,421 - app - INFO - Step 2: Searching for relevant documents...
2025-07-11 08:28:25,422 - app - INFO - Search parameters: user_id=, top_k=3, titles_filter_count=25
2025-07-11 08:28:25,425 - app.services.chatbot_services.azure_search_service - INFO - Applying titles filter: title eq 'c6e6f0c4-823d-4d82-a53b-55a3578fce2b-SOP for Calibration of Microscope.pdf' or title eq 'db342f40-4e35-47b9-94df-e59f85c7666d-SOP for Cleaning of Tablet Counter.pdf' or title eq 'fae17448-8d0a-4992-9c8d-9245507529d1-SOP for Failure Investigation.pdf' or title eq 'e573b56f-24a9-48b0-ae2e-ce6748e19fa8-SOP for Handling of Market Complaints.pdf' or title eq 'eaad21a4-ee82-48ee-a8d2-05dfffd27d9b-SOP for Material and Product Labelling in Production area.pdf' or title eq '35131a13-124f-4d82-8c6a-66cb2221d644-SOP for Process Validation.pdf' or title eq '60b6f64f-16f8-42cc-af76-7cdd7cf2ab2d-SOP for Release of Finished Product.pdf' or title eq '3f813f26-2baa-424b-a739-ae8a22fd6a46-SOP for Reprocessing and Reworking.pdf' or title eq 'e75cb50c-28bc-407b-81e4-803618d1200d-SOP for Stacking of Packed Shippers on Pallet.pdf' or title eq 'e5fa03da-0785-40d6-aa73-81e8e4a01ff9-SOP for Training of Employees.pdf' or title eq '3b3c4055-98a0-4a9c-a520-f57c7edb4c55-SOP on Quality Risk Management.txt' or title eq '279c9b9c-9cf5-4ac9-9b89-af0914c9d35b-SOP on  Handling of reserve samples.txt' or title eq 'c4eada87-34e1-4bf6-bf92-7115deec1ce6-SOP for Vendor development, Qualification   Approval and Performance Assessment.txt' or title eq 'a41f724a-05b7-48df-846f-8a9ce5903ffc-SOP-QA-001 Quality Management System Overview.pdf' or title eq '0e4eace7-6180-48bd-847a-1d03d7b531c8-SOP-QA-010 Deviation Management.pdf' or title eq 'c89f78c3-6a48-40b7-ae1a-f491cb0410e1-SOP-QA-011 Root Cause Analysis (RCA) Techniques.pdf' or title eq 'b673a6e9-a39b-4888-a066-3e93c22669ee-SOP-QA-004 Quality Risk Management (QRM) Principles.pdf' or title eq 'a825896c-705c-4998-adf9-f3ca4737f11e-20141212-Primary SOP HPLC-H column V2.pdf' or title eq '640e7744-497a-400d-9568-239bc04535c3-Standard SOP - Serious Breach Reporting.docx' or title eq '0bf11272-e113-4877-ad75-74923f9420ce-Incident Report- #10-100 - 2025-04-24 (2).pdf' or title eq '2d93ee1d-5274-4687-acee-d0c0606562f6-Quality Risk Management.txt' or title eq 'f50b2e46-fefe-4a56-9176-c972879a17af-SOP on  Handling of reserve samples - 25th Feb 2025 (1).pdf' or title eq '01ea50a0-4c6e-4653-8900-bc309b25f87d-3 July Test.pdf' or title eq '9899c046-6c91-4ef9-85d3-adc5cf73c444-SOP-QA-004 Quality Risk Management (QRM) Principles.pdf' or title eq '51bf32c6-0fc3-4b8f-bc55-b2151a0e07e6-SOP-QA-004 Quality Risk Management (QRM) Principles.pdf'
2025-07-11 08:28:27,488 - app - INFO - Document search completed. Found 4 documents
2025-07-11 08:28:27,490 - app - INFO - Step 3: Preparing context from retrieved documents...
2025-07-11 08:28:27,491 - app - INFO - Combined context preview: Document: Training of employees
Content: SOP No.:__________________
Effective Date: _______________                                                    Department: _______________
Trainer: ____________________

S. No. Name Designation Signature & Date Feedback by Trainee
(Satisfactory/

Not Satisfact...
2025-07-11 08:28:27,492 - app - INFO - Step 4: Generating AI response...
2025-07-11 08:28:30,472 - app - INFO - LLM response generated successfully: Based on the provided document context, the SOPs covered in the training program include:

1. SOP for Process Validation: This SOP outlines the requirements for validation or revalidation of manufactu...
2025-07-11 08:28:30,473 - app - INFO - Step 5: Preparing final response...
2025-07-11 08:28:30,475 - app - INFO - Response prepared successfully with 4 sources
2025-07-11 08:28:30,477 - app - INFO - Response status: Success
2025-07-11 08:28:30,479 - app - INFO - Response size: 1139 characters
2025-07-11 08:29:00,414 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-11 08:29:00,984 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
2025-07-11 08:29:10,325 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 39d4a543-f33a-4421-a298-5c00cd22c8b7: None
2025-07-11 08:29:10,650 - app.api.sop_storage - WARNING - Invalid gap_details for SOP ID 3aab5763-3703-4ae2-bdc2-c0d15ce5c7e9: None
