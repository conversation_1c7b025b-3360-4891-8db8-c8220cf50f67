-- Enable UUID extension if not already enabled
create extension if not exists "uuid-ossp";

-- Table: user_requests
create table if not exists public.user_requests (
    id uuid primary key default uuid_generate_v4(),
    user_id text,
    user_email text,
    ip_address text,
    endpoint text,
    method text,
    request_params jsonb,
    created_at timestamptz default now()
);

-- Table: request_interactions
create table if not exists public.request_interactions (
    id uuid primary key default uuid_generate_v4(),
    request_id uuid references public.user_requests(id) on delete cascade,
    step text,
    description text,
    params jsonb,
    response jsonb,
    status text,
    real_error text default null,
    created_at timestamptz default now()
);

-- Table: chat_sessions
create table if not exists public.chat_sessions (
    id uuid primary key default uuid_generate_v4(),
    session_id uuid unique not null,
    user_id text,
    user_email text,
    ip_address text,
    user_agent text,
    created_at timestamptz default now(),
    last_activity_at timestamptz default now(),
    ended_at timestamptz default null,
    status text default 'active' -- 'active', 'ended', 'expired'
);

-- Table: session_interactions (replaces request_interactions for chat sessions)
create table if not exists public.session_interactions (
    id uuid primary key default uuid_generate_v4(),
    session_id uuid references public.chat_sessions(session_id) on delete cascade,
    step text,
    description text,
    params jsonb,
    response jsonb,
    status text,
    real_error text default null,
    created_at timestamptz default now()
);

-- Optional: Indexes for faster querying
create index if not exists idx_request_interactions_request_id on public.request_interactions(request_id);
create index if not exists idx_user_requests_user_id on public.user_requests(user_id);
create index if not exists idx_chat_sessions_session_id on public.chat_sessions(session_id);
create index if not exists idx_chat_sessions_user_id on public.chat_sessions(user_id);
create index if not exists idx_session_interactions_session_id on public.session_interactions(session_id);