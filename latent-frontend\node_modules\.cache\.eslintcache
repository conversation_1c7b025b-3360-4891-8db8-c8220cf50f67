[{"C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\supabase.js": "4", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Toast\\Toast.js": "5", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\routes\\index.js": "6", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\ProtectedRoute\\ProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\Dashboard\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\ProfileSettings\\ProfileSettings.js": "9", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\Login\\Login.js": "10", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\SOPLibrary\\SOPLibrary.js": "11", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\GapAnalysis.js": "12", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\SOPLibrary\\AddSOP.js": "13", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\AssessmentForm.js": "14", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\GapDetailsSidebar.js": "15", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\LoadingSpinner\\LoadingSpinner.js": "16", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\regulationService.js": "17", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\userService.js": "18", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\api.js": "19", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\sopService.js": "20", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\config\\apiUrls.js": "21", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Navigation\\Navigation.js": "22", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\DepartmentFilters\\DepartmentFilters.js": "23", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Modal\\Modal.js": "24", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\departmentService.js": "25", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\ConfirmationModal\\ConfirmationModal.js": "26", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\AutoLogout\\AutoLogout.js": "27", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\NotFound.jsx": "28", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Layout\\Layout.js": "29", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Footer\\Footer.js": "30", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\constants\\constants.js": "31", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\utils\\sanitize.js": "32", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\URLParamChecker\\URLParamChecker.js": "33", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\ChatBot\\ChatPage.js": "34", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\queryAPI.js": "35", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\utils\\dateUtils.js": "36", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\QueryResponse\\QueryResponse.js": "37", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\QueryInput\\QueryInput.js": "38", "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\SOPDropdown\\SOPDropdown.js": "39"}, {"size": 545, "mtime": 1751308566504, "results": "40", "hashOfConfig": "41"}, {"size": 1782, "mtime": 1752166840794, "results": "42", "hashOfConfig": "41"}, {"size": 375, "mtime": 1743526400111, "results": "43", "hashOfConfig": "41"}, {"size": 323, "mtime": 1745511932093, "results": "44", "hashOfConfig": "41"}, {"size": 788, "mtime": 1745511932051, "results": "45", "hashOfConfig": "41"}, {"size": 1861, "mtime": 1752166840840, "results": "46", "hashOfConfig": "41"}, {"size": 252, "mtime": 1745511932049, "results": "47", "hashOfConfig": "41"}, {"size": 6455, "mtime": 1752166840829, "results": "48", "hashOfConfig": "41"}, {"size": 18064, "mtime": 1752166840840, "results": "49", "hashOfConfig": "41"}, {"size": 14768, "mtime": 1752166840837, "results": "50", "hashOfConfig": "41"}, {"size": 20903, "mtime": 1752166840840, "results": "51", "hashOfConfig": "41"}, {"size": 31992, "mtime": 1752166840833, "results": "52", "hashOfConfig": "41"}, {"size": 19672, "mtime": 1752166840840, "results": "53", "hashOfConfig": "41"}, {"size": 23390, "mtime": 1752166840832, "results": "54", "hashOfConfig": "41"}, {"size": 21079, "mtime": 1752166840833, "results": "55", "hashOfConfig": "41"}, {"size": 382, "mtime": 1745511932038, "results": "56", "hashOfConfig": "41"}, {"size": 314, "mtime": 1746294215408, "results": "57", "hashOfConfig": "41"}, {"size": 1453, "mtime": 1752166840854, "results": "58", "hashOfConfig": "41"}, {"size": 3863, "mtime": 1752166840840, "results": "59", "hashOfConfig": "41"}, {"size": 2766, "mtime": 1752166840852, "results": "60", "hashOfConfig": "41"}, {"size": 1483, "mtime": 1752168655928, "results": "61", "hashOfConfig": "41"}, {"size": 8562, "mtime": 1752166840817, "results": "62", "hashOfConfig": "41"}, {"size": 3099, "mtime": 1752166840801, "results": "63", "hashOfConfig": "41"}, {"size": 1808, "mtime": 1751308566476, "results": "64", "hashOfConfig": "41"}, {"size": 768, "mtime": 1752166840849, "results": "65", "hashOfConfig": "41"}, {"size": 2177, "mtime": 1752166840799, "results": "66", "hashOfConfig": "41"}, {"size": 6628, "mtime": 1752166840797, "results": "67", "hashOfConfig": "41"}, {"size": 921, "mtime": 1751308566458, "results": "68", "hashOfConfig": "41"}, {"size": 348, "mtime": 1752166840815, "results": "69", "hashOfConfig": "41"}, {"size": 671, "mtime": 1752166840810, "results": "70", "hashOfConfig": "41"}, {"size": 313, "mtime": 1751308566504, "results": "71", "hashOfConfig": "41"}, {"size": 1288, "mtime": 1751308566517, "results": "72", "hashOfConfig": "41"}, {"size": 4348, "mtime": 1752166840822, "results": "73", "hashOfConfig": "41"}, {"size": 5441, "mtime": 1752225407057, "results": "74", "hashOfConfig": "41"}, {"size": 1141, "mtime": 1752166840849, "results": "75", "hashOfConfig": "41"}, {"size": 2245, "mtime": 1752166840854, "results": "76", "hashOfConfig": "41"}, {"size": 2436, "mtime": 1752166840820, "results": "77", "hashOfConfig": "41"}, {"size": 1106, "mtime": 1752166840819, "results": "78", "hashOfConfig": "41"}, {"size": 3138, "mtime": 1752166840822, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1olh19m", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\supabase.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Toast\\Toast.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\routes\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\ProtectedRoute\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\Dashboard\\Dashboard.js", ["197", "198", "199", "200"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\ProfileSettings\\ProfileSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\Login\\Login.js", ["201", "202"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\SOPLibrary\\SOPLibrary.js", ["203"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\GapAnalysis.js", ["204"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\SOPLibrary\\AddSOP.js", ["205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\AssessmentForm.js", ["225", "226", "227", "228", "229", "230", "231", "232", "233"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\GapAnalysis\\GapDetailsSidebar.js", ["234", "235"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\LoadingSpinner\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\regulationService.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\sopService.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\config\\apiUrls.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Navigation\\Navigation.js", ["236", "237"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\DepartmentFilters\\DepartmentFilters.js", ["238"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Modal\\Modal.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\departmentService.js", ["239"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\ConfirmationModal\\ConfirmationModal.js", ["240"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\AutoLogout\\AutoLogout.js", ["241", "242", "243", "244", "245"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\Footer\\Footer.js", ["246"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\constants\\constants.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\utils\\sanitize.js", ["247"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\URLParamChecker\\URLParamChecker.js", ["248"], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\pages\\ChatBot\\ChatPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\services\\queryAPI.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\utils\\dateUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\QueryResponse\\QueryResponse.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\QueryInput\\QueryInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\src\\components\\common\\SOPDropdown\\SOPDropdown.js", [], [], {"ruleId": "249", "severity": 1, "message": "250", "line": 3, "column": 8, "nodeType": "251", "messageId": "252", "endLine": 3, "endColumn": 14}, {"ruleId": "249", "severity": 1, "message": "253", "line": 46, "column": 11, "nodeType": "251", "messageId": "252", "endLine": 46, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "253", "line": 82, "column": 11, "nodeType": "251", "messageId": "252", "endLine": 82, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "253", "line": 111, "column": 11, "nodeType": "251", "messageId": "252", "endLine": 111, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "254", "line": 22, "column": 32, "nodeType": "251", "messageId": "252", "endLine": 22, "endColumn": 55}, {"ruleId": "255", "severity": 1, "message": "256", "line": 317, "column": 13, "nodeType": "257", "endLine": 317, "endColumn": 123}, {"ruleId": "249", "severity": 1, "message": "258", "line": 287, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 287, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "253", "line": 281, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 281, "endColumn": 21}, {"ruleId": "249", "severity": 1, "message": "259", "line": 3, "column": 8, "nodeType": "251", "messageId": "252", "endLine": 3, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "260", "line": 14, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "261", "line": 28, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 28, "endColumn": 29}, {"ruleId": "249", "severity": 1, "message": "262", "line": 30, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 30, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "263", "line": 35, "column": 39, "nodeType": "251", "messageId": "252", "endLine": 35, "endColumn": 69}, {"ruleId": "249", "severity": 1, "message": "264", "line": 37, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 37, "endColumn": 32}, {"ruleId": "249", "severity": 1, "message": "265", "line": 37, "column": 34, "nodeType": "251", "messageId": "252", "endLine": 37, "endColumn": 59}, {"ruleId": "249", "severity": 1, "message": "266", "line": 38, "column": 32, "nodeType": "251", "messageId": "252", "endLine": 38, "endColumn": 55}, {"ruleId": "249", "severity": 1, "message": "267", "line": 39, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 39, "endColumn": 29}, {"ruleId": "249", "severity": 1, "message": "268", "line": 40, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 40, "endColumn": 27}, {"ruleId": "249", "severity": 1, "message": "269", "line": 41, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 41, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "270", "line": 45, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 45, "endColumn": 30}, {"ruleId": "271", "severity": 1, "message": "272", "line": 186, "column": 6, "nodeType": "273", "endLine": 186, "endColumn": 19, "suggestions": "274"}, {"ruleId": "249", "severity": 1, "message": "275", "line": 199, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 199, "endColumn": 31}, {"ruleId": "249", "severity": 1, "message": "276", "line": 203, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 203, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "277", "line": 211, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 211, "endColumn": 33}, {"ruleId": "249", "severity": 1, "message": "278", "line": 259, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 259, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "279", "line": 264, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 264, "endColumn": 19}, {"ruleId": "249", "severity": 1, "message": "280", "line": 292, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 292, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "281", "line": 358, "column": 13, "nodeType": "251", "messageId": "252", "endLine": 358, "endColumn": 21}, {"ruleId": "249", "severity": 1, "message": "282", "line": 15, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 15, "endColumn": 21}, {"ruleId": "249", "severity": 1, "message": "283", "line": 17, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 17, "endColumn": 32}, {"ruleId": "249", "severity": 1, "message": "284", "line": 39, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 39, "endColumn": 28}, {"ruleId": "249", "severity": 1, "message": "285", "line": 44, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 44, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "286", "line": 45, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 45, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "287", "line": 147, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 147, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "276", "line": 165, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 165, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "288", "line": 195, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 195, "endColumn": 27}, {"ruleId": "249", "severity": 1, "message": "267", "line": 209, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 209, "endColumn": 28}, {"ruleId": "249", "severity": 1, "message": "289", "line": 7, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 7, "endColumn": 22}, {"ruleId": "249", "severity": 1, "message": "290", "line": 191, "column": 15, "nodeType": "251", "messageId": "252", "endLine": 191, "endColumn": 37}, {"ruleId": "249", "severity": 1, "message": "253", "line": 35, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 35, "endColumn": 21}, {"ruleId": "255", "severity": 1, "message": "256", "line": 67, "column": 13, "nodeType": "257", "endLine": 67, "endColumn": 123}, {"ruleId": "249", "severity": 1, "message": "253", "line": 38, "column": 11, "nodeType": "251", "messageId": "252", "endLine": 38, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "253", "line": 16, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 16, "endColumn": 21}, {"ruleId": "249", "severity": 1, "message": "291", "line": 1, "column": 17, "nodeType": "251", "messageId": "252", "endLine": 1, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "260", "line": 32, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 32, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "253", "line": 93, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 93, "endColumn": 21}, {"ruleId": "271", "severity": 1, "message": "292", "line": 125, "column": 6, "nodeType": "273", "endLine": 125, "endColumn": 20, "suggestions": "293"}, {"ruleId": "271", "severity": 1, "message": "294", "line": 153, "column": 6, "nodeType": "273", "endLine": 153, "endColumn": 20, "suggestions": "295"}, {"ruleId": "271", "severity": 1, "message": "294", "line": 180, "column": 6, "nodeType": "273", "endLine": 180, "endColumn": 64, "suggestions": "296"}, {"ruleId": "249", "severity": 1, "message": "297", "line": 4, "column": 9, "nodeType": "251", "messageId": "252", "endLine": 4, "endColumn": 20}, {"ruleId": "298", "severity": 1, "message": "299", "line": 41, "column": 1, "nodeType": "300", "endLine": 44, "endColumn": 3}, {"ruleId": "249", "severity": 1, "message": "301", "line": 1, "column": 8, "nodeType": "251", "messageId": "252", "endLine": 1, "endColumn": 13}, "no-unused-vars", "'Layout' is defined but never used.", "Identifier", "unusedVar", "'errorMessage' is assigned a value but never used.", "'setIsConfirmationAccess' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'handleSOPAdded' is assigned a value but never used.", "'departmentService' is defined but never used.", "'navigate' is assigned a value but never used.", "'filteredDepartments' is assigned a value but never used.", "'departmentError' is assigned a value but never used.", "'setSelectedComplianceStandards' is assigned a value but never used.", "'showRegulationDropdown' is assigned a value but never used.", "'setShowRegulationDropdown' is assigned a value but never used.", "'setRegulationSearchTerm' is assigned a value but never used.", "'filteredRegulations' is assigned a value but never used.", "'regulationLoading' is assigned a value but never used.", "'regulationError' is assigned a value but never used.", "'regulationDropdownRef' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.selectedDepartment'. Either include it or remove the dependency array.", "ArrayExpression", ["302"], "'handleDepartmentSearch' is assigned a value but never used.", "'selectDepartment' is assigned a value but never used.", "'toggleDepartmentDropdown' is assigned a value but never used.", "'handleDragOver' is assigned a value but never used.", "'handleDrop' is assigned a value but never used.", "'handleBrowseClick' is assigned a value but never used.", "'response' is assigned a value but never used.", "'departments' is assigned a value but never used.", "'showDepartmentDropdown' is assigned a value but never used.", "'departmentsLoading' is assigned a value but never used.", "'departmentsError' is assigned a value but never used.", "'regulationsError' is assigned a value but never used.", "'handleInputChange' is assigned a value but never used.", "'handleSearchChange' is assigned a value but never used.", "'sanitizeHtml' is defined but never used.", "'updatedAnalysisResults' is assigned a value but never used.", "'useRef' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleUserActivity'. Either include it or remove the dependency array.", ["303"], "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", ["304"], ["305"], "'currentYear' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'React' is defined but never used.", {"desc": "306", "fix": "307"}, {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, "Update the dependencies array to be: [departments, formData.selectedDepartment]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [handleUserActivity, warningShown]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [handleLogout, warningShown]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [handleLogout, lastActivity, timeoutDuration, warningShown, warningTime]", {"range": "320", "text": "321"}, [6797, 6810], "[departments, formData.selectedDepartment]", [4262, 4276], "[handleUserActivity, warningShown]", [5183, 5197], "[handle<PERSON><PERSON><PERSON>, warningShown]", [6119, 6177], "[handleLogout, lastActivity, timeoutDuration, warningShown, warningTime]"]