{"ast": null, "code": "import API_URLS from \"../config/apiUrls\";\nimport apiService from \"./api\";\nexport const queryAPI = async (query, userId = null, filterTitles = null, chatHistory = [], blobToTitleMapping = null, sessionId = null) => {\n  try {\n    const requestBody = {\n      query,\n      user_id: userId,\n      filter_titles: [],\n      chat_history: [],\n      session_id: sessionId\n    };\n\n    // Add filter titles if provided\n    if (filterTitles && filterTitles.length > 0) {\n      requestBody.filter_titles = filterTitles;\n    }\n\n    // Add chat history if provided\n    if (chatHistory && chatHistory.length > 0) {\n      requestBody.chat_history = chatHistory;\n    }\n\n    // Add blob to title mapping if provided\n    if (blobToTitleMapping) {\n      requestBody.blob_to_title_mapping = blobToTitleMapping;\n    }\n\n    //console.log(\"Making request to:\", API_URLS.CHAT.QUERY);\n    //console.log(\"With body:\", requestBody);\n    const response = await apiService.post(API_URLS.CHAT.QUERY, requestBody);\n    //console.log(\"Got response:\", response);\n    return response;\n  } catch (error) {\n    console.error(\"API Error:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_URLS", "apiService", "queryAPI", "query", "userId", "filterTitles", "chatHistory", "blobToTitleMapping", "sessionId", "requestBody", "user_id", "filter_titles", "chat_history", "session_id", "length", "blob_to_title_mapping", "response", "post", "CHAT", "QUERY", "error", "console"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/queryAPI.js"], "sourcesContent": ["import API_URLS from \"../config/apiUrls\";\r\nimport apiService from \"./api\";\r\n\r\nexport const queryAPI = async (\r\n  query,\r\n  userId = null,\r\n  filterTitles = null,\r\n  chatHistory = [],\r\n  blobToTitleMapping = null,\r\n  sessionId = null\r\n) => {\r\n  try {\r\n    const requestBody = {\r\n      query,\r\n      user_id: userId,\r\n      filter_titles: [],\r\n      chat_history: [],\r\n      session_id: sessionId,\r\n    };\r\n\r\n    // Add filter titles if provided\r\n    if (filterTitles && filterTitles.length > 0) {\r\n      requestBody.filter_titles = filterTitles;\r\n    }\r\n\r\n    // Add chat history if provided\r\n    if (chatHistory && chatHistory.length > 0) {\r\n      requestBody.chat_history = chatHistory;\r\n    }\r\n\r\n    // Add blob to title mapping if provided\r\n    if (blobToTitleMapping) {\r\n      requestBody.blob_to_title_mapping = blobToTitleMapping;\r\n    }\r\n\r\n    //console.log(\"Making request to:\", API_URLS.CHAT.QUERY);\r\n    //console.log(\"With body:\", requestBody);\r\n    const response = await apiService.post(API_URLS.CHAT.QUERY, requestBody);\r\n    //console.log(\"Got response:\", response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"API Error:\", error);\r\n    throw error;\r\n  }\r\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,UAAU,MAAM,OAAO;AAE9B,OAAO,MAAMC,QAAQ,GAAG,MAAAA,CACtBC,KAAK,EACLC,MAAM,GAAG,IAAI,EACbC,YAAY,GAAG,IAAI,EACnBC,WAAW,GAAG,EAAE,EAChBC,kBAAkB,GAAG,IAAI,EACzBC,SAAS,GAAG,IAAI,KACb;EACH,IAAI;IACF,MAAMC,WAAW,GAAG;MAClBN,KAAK;MACLO,OAAO,EAAEN,MAAM;MACfO,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAEL;IACd,CAAC;;IAED;IACA,IAAIH,YAAY,IAAIA,YAAY,CAACS,MAAM,GAAG,CAAC,EAAE;MAC3CL,WAAW,CAACE,aAAa,GAAGN,YAAY;IAC1C;;IAEA;IACA,IAAIC,WAAW,IAAIA,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;MACzCL,WAAW,CAACG,YAAY,GAAGN,WAAW;IACxC;;IAEA;IACA,IAAIC,kBAAkB,EAAE;MACtBE,WAAW,CAACM,qBAAqB,GAAGR,kBAAkB;IACxD;;IAEA;IACA;IACA,MAAMS,QAAQ,GAAG,MAAMf,UAAU,CAACgB,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACC,KAAK,EAAEV,WAAW,CAAC;IACxE;IACA,OAAOO,QAAQ;EACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}