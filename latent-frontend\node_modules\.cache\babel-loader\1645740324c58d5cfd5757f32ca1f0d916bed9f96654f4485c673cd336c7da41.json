{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 3.1c-.5 0-1-.1-1.5-.1s-1 .1-1.5.1\",\n  key: \"16ll65\"\n}], [\"path\", {\n  d: \"M19.3 6.8a10.45 10.45 0 0 0-2.1-2.1\",\n  key: \"1nq77a\"\n}], [\"path\", {\n  d: \"M20.9 13.5c.1-.5.1-1 .1-1.5s-.1-1-.1-1.5\",\n  key: \"1sf7wn\"\n}], [\"path\", {\n  d: \"M17.2 19.3a10.45 10.45 0 0 0 2.1-2.1\",\n  key: \"x1hs5g\"\n}], [\"path\", {\n  d: \"M10.5 20.9c.5.1 1 .1 1.5.1s1-.1 1.5-.1\",\n  key: \"19m18z\"\n}], [\"path\", {\n  d: \"M3.5 17.5 2 22l4.5-1.5\",\n  key: \"1f36qi\"\n}], [\"path\", {\n  d: \"M3.1 10.5c0 .5-.1 1-.1 1.5s.1 1 .1 1.5\",\n  key: \"1vz3ju\"\n}], [\"path\", {\n  d: \"M6.8 4.7a10.45 10.45 0 0 0-2.1 2.1\",\n  key: \"19f9do\"\n}]];\nconst MessageCircleDashed = createLucideIcon(\"message-circle-dashed\", __iconNode);\nexport { __iconNode, MessageCircleDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageCircleDashed", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\node_modules\\lucide-react\\src\\icons\\message-circle-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.5 3.1c-.5 0-1-.1-1.5-.1s-1 .1-1.5.1', key: '16ll65' }],\n  ['path', { d: 'M19.3 6.8a10.45 10.45 0 0 0-2.1-2.1', key: '1nq77a' }],\n  ['path', { d: 'M20.9 13.5c.1-.5.1-1 .1-1.5s-.1-1-.1-1.5', key: '1sf7wn' }],\n  ['path', { d: 'M17.2 19.3a10.45 10.45 0 0 0 2.1-2.1', key: 'x1hs5g' }],\n  ['path', { d: 'M10.5 20.9c.5.1 1 .1 1.5.1s1-.1 1.5-.1', key: '19m18z' }],\n  ['path', { d: 'M3.5 17.5 2 22l4.5-1.5', key: '1f36qi' }],\n  ['path', { d: 'M3.1 10.5c0 .5-.1 1-.1 1.5s.1 1 .1 1.5', key: '1vz3ju' }],\n  ['path', { d: 'M6.8 4.7a10.45 10.45 0 0 0-2.1 2.1', key: '19f9do' }],\n];\n\n/**\n * @component @name MessageCircleDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuNSAzLjFjLS41IDAtMS0uMS0xLjUtLjFzLTEgLjEtMS41LjEiIC8+CiAgPHBhdGggZD0iTTE5LjMgNi44YTEwLjQ1IDEwLjQ1IDAgMCAwLTIuMS0yLjEiIC8+CiAgPHBhdGggZD0iTTIwLjkgMTMuNWMuMS0uNS4xLTEgLjEtMS41cy0uMS0xLS4xLTEuNSIgLz4KICA8cGF0aCBkPSJNMTcuMiAxOS4zYTEwLjQ1IDEwLjQ1IDAgMCAwIDIuMS0yLjEiIC8+CiAgPHBhdGggZD0iTTEwLjUgMjAuOWMuNS4xIDEgLjEgMS41LjFzMS0uMSAxLjUtLjEiIC8+CiAgPHBhdGggZD0iTTMuNSAxNy41IDIgMjJsNC41LTEuNSIgLz4KICA8cGF0aCBkPSJNMy4xIDEwLjVjMCAuNS0uMSAxLS4xIDEuNXMuMSAxIC4xIDEuNSIgLz4KICA8cGF0aCBkPSJNNi44IDQuN2ExMC40NSAxMC40NSAwIDAgMC0yLjEgMi4xIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-circle-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircleDashed = createLucideIcon('message-circle-dashed', __iconNode);\n\nexport default MessageCircleDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAU,GACrE;AAaM,MAAAC,mBAAA,GAAsBC,gBAAiB,0BAAyBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}