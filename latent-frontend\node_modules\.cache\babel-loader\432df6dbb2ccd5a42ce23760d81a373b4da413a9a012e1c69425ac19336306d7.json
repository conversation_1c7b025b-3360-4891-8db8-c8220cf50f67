{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\ChatBot\\\\ChatPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useMemo } from \"react\";\nimport Navigation from \"../../common/Navigation/Navigation\";\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\nimport { queryAPI } from \"../../../services/queryAPI\";\nimport \"./ChatPage.css\";\nimport { toast } from \"react-toastify\";\nimport sopService from \"../../../services/sopService\";\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\n\n// Generate UUID for session management\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst generateUUID = () => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\nfunction ChatPage() {\n  _s();\n  const [chatHistory, setChatHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\n  const [sops, setSops] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n\n  // Initialize session ID on component mount\n  useEffect(() => {\n    // Check if this is a fresh page load (not just navigation within the app)\n    const isPageRefresh = !sessionStorage.getItem('appNavigated');\n    let currentSessionId = sessionStorage.getItem('chatSessionId');\n    if (!currentSessionId || isPageRefresh) {\n      // Generate new session ID if none exists or if page was refreshed\n      currentSessionId = generateUUID();\n      sessionStorage.setItem('chatSessionId', currentSessionId);\n      console.log('New chat session created:', currentSessionId);\n    } else {\n      console.log('Existing chat session found:', currentSessionId);\n    }\n\n    // Mark that we've navigated within the app\n    sessionStorage.setItem('appNavigated', 'true');\n    setSessionId(currentSessionId);\n  }, []);\n  useEffect(() => {\n    const fetchSOPs = async () => {\n      setLoading(true);\n      try {\n        const data = await sopService.getAllSOPs();\n        //console.log(\"Fetched SOPs:\", data);\n        setSops(data.sops_data);\n      } catch (e) {\n        setSops([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSOPs();\n  }, []);\n  useEffect(() => {\n    const savedHistory = sessionStorage.getItem('chatHistory');\n    if (savedHistory) {\n      setChatHistory(JSON.parse(savedHistory));\n    }\n  }, []);\n  useEffect(() => {\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\n  }, [chatHistory]);\n  useEffect(() => {\n    const handleRefresh = () => {\n      sessionStorage.removeItem('chatHistory');\n    };\n    window.addEventListener('beforeunload', handleRefresh);\n    return () => window.removeEventListener('beforeunload', handleRefresh);\n  }, []);\n  const {\n    allowedTitles,\n    titleToBlobMap\n  } = useMemo(() => {\n    const mapping = {};\n    const titles = [];\n    sops.forEach(sop => {\n      if (sop.title && sop.blob_file_name) {\n        mapping[sop.title] = sop.blob_file_name;\n        titles.push(sop.title);\n      }\n    });\n    return {\n      allowedTitles: titles,\n      titleToBlobMap: mapping\n    };\n  }, [sops]);\n  const handleQuery = async query => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const filterTitles = selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\n      const filterBlobNames = filterTitles.map(title => titleToBlobMap[title]).filter(Boolean);\n      const blobToTitleMapping = {};\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\n        blobToTitleMapping[blobName] = title;\n      });\n      const historyForAPI = chatHistory.map(turn => ({\n        user_query: turn.query,\n        bot_answer: turn.response.answer\n      }));\n      const result = await queryAPI(query, null, filterBlobNames, historyForAPI, blobToTitleMapping, sessionId);\n      setChatHistory(prevHistory => [...prevHistory, {\n        query,\n        response: result,\n        timestamp: new Date().toISOString()\n      }]);\n    } catch (err) {\n      setError(err.message || \"An error occurred while processing your query\");\n      console.error(\"Query error:\", err);\n      toast.error(\"Error processing your query\", {\n        toastId: `query-error-${Date.now()}`\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSOPChange = newSelectedSOPs => {\n    setSelectedSOPs(newSelectedSOPs);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chatbot-page\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"chat-container\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-box\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-text\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), chatHistory.length === 0 && !isLoading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"welcome-title\",\n          children: \"Welcome to Regulation & Compliance Assistant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"welcome-subtitle\",\n          children: \"Ask questions about your SOPs and Regulations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SOPDropdown, {\n          sopTitles: allowedTitles,\n          selectedSOPs: selectedSOPs,\n          onSOPChange: handleSOPChange,\n          isLoading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-hints\",\n          children: selectedSOPs.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"filter-indicator\",\n            children: \"\\u2022 Filtering responses to selected documents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), chatHistory.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-history\",\n        children: chatHistory.map((chat, index) => /*#__PURE__*/_jsxDEV(QueryResponse, {\n          query: chat.query,\n          response: chat.response,\n          timestamp: chat.timestamp\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-box\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Processing your query...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(QueryInput, {\n        onSubmit: handleQuery,\n        isLoading: isLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPage, \"cjd0VLqr2UlXVrFEUYBFm8nLOio=\");\n_c = ChatPage;\nexport default ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "useMemo", "Navigation", "QueryInput", "QueryResponse", "SOPDropdown", "queryAPI", "toast", "sopService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "generateUUID", "replace", "c", "r", "Math", "random", "v", "toString", "ChatPage", "_s", "chatHistory", "setChatHistory", "isLoading", "setIsLoading", "error", "setError", "selectedSOPs", "setSelectedSOPs", "sops", "setSops", "loading", "setLoading", "sessionId", "setSessionId", "isPageRefresh", "sessionStorage", "getItem", "currentSessionId", "setItem", "console", "log", "fetchSOPs", "data", "getAllSOPs", "sops_data", "e", "savedHistory", "JSON", "parse", "stringify", "handleRefresh", "removeItem", "window", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "titleToBlobMap", "mapping", "titles", "for<PERSON>ach", "sop", "title", "blob_file_name", "push", "handleQuery", "query", "filterTitles", "length", "filterBlobNames", "map", "filter", "Boolean", "blobToTitleMapping", "Object", "entries", "blobName", "historyForAPI", "turn", "user_query", "bot_answer", "response", "answer", "result", "prevHistory", "timestamp", "Date", "toISOString", "err", "message", "toastId", "now", "handleSOPChange", "newSelectedSOPs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sop<PERSON><PERSON><PERSON>", "onSOPChange", "chat", "index", "size", "onSubmit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/ChatBot/ChatPage.js"], "sourcesContent": ["import { useState, useEffect, useMemo } from \"react\";\r\nimport Navigation from \"../../common/Navigation/Navigation\";\r\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\r\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\r\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\r\nimport { queryAPI } from \"../../../services/queryAPI\";\r\nimport \"./ChatPage.css\";\r\nimport { toast } from \"react-toastify\";\r\nimport sopService from \"../../../services/sopService\";\r\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\r\n\r\n// Generate UUID for session management\r\nconst generateUUID = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n    const r = Math.random() * 16 | 0;\r\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n};\r\n\r\nfunction ChatPage() {\r\n  const [chatHistory, setChatHistory] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\r\n  const [sops, setSops] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [sessionId, setSessionId] = useState(null);\r\n\r\n  // Initialize session ID on component mount\r\n  useEffect(() => {\r\n    // Check if this is a fresh page load (not just navigation within the app)\r\n    const isPageRefresh = !sessionStorage.getItem('appNavigated');\r\n    let currentSessionId = sessionStorage.getItem('chatSessionId');\r\n\r\n    if (!currentSessionId || isPageRefresh) {\r\n      // Generate new session ID if none exists or if page was refreshed\r\n      currentSessionId = generateUUID();\r\n      sessionStorage.setItem('chatSessionId', currentSessionId);\r\n      console.log('New chat session created:', currentSessionId);\r\n    } else {\r\n      console.log('Existing chat session found:', currentSessionId);\r\n    }\r\n\r\n    // Mark that we've navigated within the app\r\n    sessionStorage.setItem('appNavigated', 'true');\r\n    setSessionId(currentSessionId);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchSOPs = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const data = await sopService.getAllSOPs();\r\n        //console.log(\"Fetched SOPs:\", data);\r\n        setSops(data.sops_data);\r\n      } catch (e) {\r\n        setSops([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchSOPs();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const savedHistory = sessionStorage.getItem('chatHistory');\r\n    if (savedHistory) {\r\n      setChatHistory(JSON.parse(savedHistory));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\r\n  }, [chatHistory]);\r\n\r\n  useEffect(() => {\r\n  const handleRefresh = () => {\r\n    sessionStorage.removeItem('chatHistory');\r\n  };\r\n  window.addEventListener('beforeunload', handleRefresh);\r\n  return () => window.removeEventListener('beforeunload', handleRefresh);\r\n}, []);\r\n\r\n  \r\n\r\n  const { allowedTitles, titleToBlobMap } = useMemo(() => {\r\n    const mapping = {};\r\n    const titles = [];\r\n    sops.forEach((sop) => {\r\n      if (sop.title && sop.blob_file_name) {\r\n        mapping[sop.title] = sop.blob_file_name;\r\n        titles.push(sop.title);\r\n      }\r\n    });\r\n    return { allowedTitles: titles, titleToBlobMap: mapping };\r\n  }, [sops]);\r\n\r\n  const handleQuery = async (query) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const filterTitles =\r\n        selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\r\n\r\n      const filterBlobNames = filterTitles\r\n        .map((title) => titleToBlobMap[title])\r\n        .filter(Boolean);\r\n\r\n      const blobToTitleMapping = {};\r\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\r\n        blobToTitleMapping[blobName] = title;\r\n      });\r\n\r\n      const historyForAPI = chatHistory.map((turn) => ({\r\n        user_query: turn.query,\r\n        bot_answer: turn.response.answer,\r\n      }));\r\n\r\n      const result = await queryAPI(\r\n        query,\r\n        null,\r\n        filterBlobNames,\r\n        historyForAPI,\r\n        blobToTitleMapping,\r\n        sessionId\r\n      );\r\n\r\n      setChatHistory((prevHistory) => [\r\n        ...prevHistory,\r\n        {\r\n          query,\r\n          response: result,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      ]);\r\n    } catch (err) {\r\n      setError(err.message || \"An error occurred while processing your query\");\r\n      console.error(\"Query error:\", err);\r\n      toast.error(\"Error processing your query\", {\r\n        toastId: `query-error-${Date.now()}`,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSOPChange = (newSelectedSOPs) => {\r\n    setSelectedSOPs(newSelectedSOPs);\r\n  };\r\n\r\n  return (\r\n    <div className=\"chatbot-page\">\r\n      <Navigation />\r\n      <main className=\"chat-container\">\r\n        {error && (\r\n          <div className=\"error-box\">\r\n            <p className=\"error-text\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length === 0 && !isLoading && !error && (\r\n          <div className=\"welcome-section\">\r\n            \r\n            <h2 className=\"welcome-title\">\r\n              Welcome to Regulation & Compliance Assistant\r\n            </h2>\r\n            <p className=\"welcome-subtitle\">\r\n              Ask questions about your SOPs and Regulations\r\n            </p>\r\n\r\n            <SOPDropdown\r\n              sopTitles={allowedTitles}\r\n              selectedSOPs={selectedSOPs}\r\n              onSOPChange={handleSOPChange}\r\n              isLoading={loading}\r\n            />\r\n\r\n            <div className=\"welcome-hints\">\r\n              {selectedSOPs.length > 0 && (\r\n                <p className=\"filter-indicator\">\r\n                  • Filtering responses to selected documents\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length > 0 && (\r\n          <div className=\"chat-history\">\r\n            {chatHistory.map((chat, index) => (\r\n              <QueryResponse\r\n                key={index}\r\n                query={chat.query}\r\n                response={chat.response}\r\n                timestamp={chat.timestamp}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {isLoading && (\r\n          <div className=\"loading-box\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <p className=\"loading-text\">Processing your query...</p>\r\n          </div>\r\n        )}\r\n\r\n        <QueryInput onSubmit={handleQuery} isLoading={isLoading} />\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatPage;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACpD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,gBAAgB;AACvB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,cAAc,MAAM,4CAA4C;;AAEvE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;IACzE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAChC,MAAMC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;IACzC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AAED,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoC,aAAa,GAAG,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC;IAC7D,IAAIC,gBAAgB,GAAGF,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;IAE9D,IAAI,CAACC,gBAAgB,IAAIH,aAAa,EAAE;MACtC;MACAG,gBAAgB,GAAG3B,YAAY,CAAC,CAAC;MACjCyB,cAAc,CAACG,OAAO,CAAC,eAAe,EAAED,gBAAgB,CAAC;MACzDE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,gBAAgB,CAAC;IAC5D,CAAC,MAAM;MACLE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEH,gBAAgB,CAAC;IAC/D;;IAEA;IACAF,cAAc,CAACG,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;IAC9CL,YAAY,CAACI,gBAAgB,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd,MAAM2C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BV,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMW,IAAI,GAAG,MAAMpC,UAAU,CAACqC,UAAU,CAAC,CAAC;QAC1C;QACAd,OAAO,CAACa,IAAI,CAACE,SAAS,CAAC;MACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVhB,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDU,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN3C,SAAS,CAAC,MAAM;IACd,MAAMgD,YAAY,GAAGX,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;IAC1D,IAAIU,YAAY,EAAE;MAChBzB,cAAc,CAAC0B,IAAI,CAACC,KAAK,CAACF,YAAY,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,MAAM;IACdqC,cAAc,CAACG,OAAO,CAAC,aAAa,EAAES,IAAI,CAACE,SAAS,CAAC7B,WAAW,CAAC,CAAC;EACpE,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBtB,SAAS,CAAC,MAAM;IAChB,MAAMoD,aAAa,GAAGA,CAAA,KAAM;MAC1Bf,cAAc,CAACgB,UAAU,CAAC,aAAa,CAAC;IAC1C,CAAC;IACDC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEH,aAAa,CAAC;IACtD,OAAO,MAAME,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEJ,aAAa,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAIJ,MAAM;IAAEK,aAAa;IAAEC;EAAe,CAAC,GAAGzD,OAAO,CAAC,MAAM;IACtD,MAAM0D,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,EAAE;IACjB9B,IAAI,CAAC+B,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,cAAc,EAAE;QACnCL,OAAO,CAACG,GAAG,CAACC,KAAK,CAAC,GAAGD,GAAG,CAACE,cAAc;QACvCJ,MAAM,CAACK,IAAI,CAACH,GAAG,CAACC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC;IACF,OAAO;MAAEN,aAAa,EAAEG,MAAM;MAAEF,cAAc,EAAEC;IAAQ,CAAC;EAC3D,CAAC,EAAE,CAAC7B,IAAI,CAAC,CAAC;EAEV,MAAMoC,WAAW,GAAG,MAAOC,KAAK,IAAK;IACnC1C,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMyC,YAAY,GAChBxC,YAAY,CAACyC,MAAM,GAAG,CAAC,GAAGzC,YAAY,GAAG6B,aAAa;MAExD,MAAMa,eAAe,GAAGF,YAAY,CACjCG,GAAG,CAAER,KAAK,IAAKL,cAAc,CAACK,KAAK,CAAC,CAAC,CACrCS,MAAM,CAACC,OAAO,CAAC;MAElB,MAAMC,kBAAkB,GAAG,CAAC,CAAC;MAC7BC,MAAM,CAACC,OAAO,CAAClB,cAAc,CAAC,CAACG,OAAO,CAAC,CAAC,CAACE,KAAK,EAAEc,QAAQ,CAAC,KAAK;QAC5DH,kBAAkB,CAACG,QAAQ,CAAC,GAAGd,KAAK;MACtC,CAAC,CAAC;MAEF,MAAMe,aAAa,GAAGxD,WAAW,CAACiD,GAAG,CAAEQ,IAAI,KAAM;QAC/CC,UAAU,EAAED,IAAI,CAACZ,KAAK;QACtBc,UAAU,EAAEF,IAAI,CAACG,QAAQ,CAACC;MAC5B,CAAC,CAAC,CAAC;MAEH,MAAMC,MAAM,GAAG,MAAM9E,QAAQ,CAC3B6D,KAAK,EACL,IAAI,EACJG,eAAe,EACfQ,aAAa,EACbJ,kBAAkB,EAClBxC,SACF,CAAC;MAEDX,cAAc,CAAE8D,WAAW,IAAK,CAC9B,GAAGA,WAAW,EACd;QACElB,KAAK;QACLe,QAAQ,EAAEE,MAAM;QAChBE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ9D,QAAQ,CAAC8D,GAAG,CAACC,OAAO,IAAI,+CAA+C,CAAC;MACxEjD,OAAO,CAACf,KAAK,CAAC,cAAc,EAAE+D,GAAG,CAAC;MAClClF,KAAK,CAACmB,KAAK,CAAC,6BAA6B,EAAE;QACzCiE,OAAO,EAAE,eAAeJ,IAAI,CAACK,GAAG,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMoE,eAAe,GAAIC,eAAe,IAAK;IAC3CjE,eAAe,CAACiE,eAAe,CAAC;EAClC,CAAC;EAED,oBACEnF,OAAA;IAAKoF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BrF,OAAA,CAACT,UAAU;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdzF,OAAA;MAAMoF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC7BtE,KAAK,iBACJf,OAAA;QAAKoF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrF,OAAA;UAAGoF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEtE;QAAK;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACN,EAEA9E,WAAW,CAAC+C,MAAM,KAAK,CAAC,IAAI,CAAC7C,SAAS,IAAI,CAACE,KAAK,iBAC/Cf,OAAA;QAAKoF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BrF,OAAA;UAAIoF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzF,OAAA;UAAGoF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJzF,OAAA,CAACN,WAAW;UACVgG,SAAS,EAAE5C,aAAc;UACzB7B,YAAY,EAAEA,YAAa;UAC3B0E,WAAW,EAAET,eAAgB;UAC7BrE,SAAS,EAAEQ;QAAQ;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEFzF,OAAA;UAAKoF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BpE,YAAY,CAACyC,MAAM,GAAG,CAAC,iBACtB1D,OAAA;YAAGoF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA9E,WAAW,CAAC+C,MAAM,GAAG,CAAC,iBACrB1D,OAAA;QAAKoF,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B1E,WAAW,CAACiD,GAAG,CAAC,CAACgC,IAAI,EAAEC,KAAK,kBAC3B7F,OAAA,CAACP,aAAa;UAEZ+D,KAAK,EAAEoC,IAAI,CAACpC,KAAM;UAClBe,QAAQ,EAAEqB,IAAI,CAACrB,QAAS;UACxBI,SAAS,EAAEiB,IAAI,CAACjB;QAAU,GAHrBkB,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEA5E,SAAS,iBACRb,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrF,OAAA,CAACF,cAAc;UAACgG,IAAI,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BzF,OAAA;UAAGoF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN,eAEDzF,OAAA,CAACR,UAAU;QAACuG,QAAQ,EAAExC,WAAY;QAAC1C,SAAS,EAAEA;MAAU;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC/E,EAAA,CAjMQD,QAAQ;AAAAuF,EAAA,GAARvF,QAAQ;AAmMjB,eAAeA,QAAQ;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}