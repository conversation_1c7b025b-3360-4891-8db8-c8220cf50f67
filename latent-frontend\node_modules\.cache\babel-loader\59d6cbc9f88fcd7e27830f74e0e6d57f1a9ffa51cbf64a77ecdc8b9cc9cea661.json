{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\ChatBot\\\\ChatPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useMemo } from \"react\";\nimport Navigation from \"../../common/Navigation/Navigation\";\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\nimport { queryAPI } from \"../../../services/queryAPI\";\nimport \"./ChatPage.css\";\nimport { toast } from \"react-toastify\";\nimport sopService from \"../../../services/sopService\";\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\n\n// Generate UUID for session management\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst generateUUID = () => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\nfunction ChatPage() {\n  _s();\n  const [chatHistory, setChatHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\n  const [sops, setSops] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n\n  // Initialize session ID on component mount\n  useEffect(() => {\n    // Always generate a new session ID on component mount (page load/refresh)\n    // This ensures each browser refresh creates a new chat session\n    const currentSessionId = generateUUID();\n    sessionStorage.setItem('chatSessionId', currentSessionId);\n    setSessionId(currentSessionId);\n    console.log('New chat session created on page load:', currentSessionId);\n  }, []);\n  useEffect(() => {\n    const fetchSOPs = async () => {\n      setLoading(true);\n      try {\n        const data = await sopService.getAllSOPs();\n        //console.log(\"Fetched SOPs:\", data);\n        setSops(data.sops_data);\n      } catch (e) {\n        setSops([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSOPs();\n  }, []);\n  useEffect(() => {\n    const savedHistory = sessionStorage.getItem('chatHistory');\n    if (savedHistory) {\n      setChatHistory(JSON.parse(savedHistory));\n    }\n  }, []);\n  useEffect(() => {\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\n  }, [chatHistory]);\n  useEffect(() => {\n    const handleRefresh = () => {\n      sessionStorage.removeItem('chatHistory');\n    };\n    window.addEventListener('beforeunload', handleRefresh);\n    return () => window.removeEventListener('beforeunload', handleRefresh);\n  }, []);\n  const {\n    allowedTitles,\n    titleToBlobMap\n  } = useMemo(() => {\n    const mapping = {};\n    const titles = [];\n    sops.forEach(sop => {\n      if (sop.title && sop.blob_file_name) {\n        mapping[sop.title] = sop.blob_file_name;\n        titles.push(sop.title);\n      }\n    });\n    return {\n      allowedTitles: titles,\n      titleToBlobMap: mapping\n    };\n  }, [sops]);\n  const handleQuery = async query => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const filterTitles = selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\n      const filterBlobNames = filterTitles.map(title => titleToBlobMap[title]).filter(Boolean);\n      const blobToTitleMapping = {};\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\n        blobToTitleMapping[blobName] = title;\n      });\n      const historyForAPI = chatHistory.map(turn => ({\n        user_query: turn.query,\n        bot_answer: turn.response.answer\n      }));\n      const result = await queryAPI(query, null, filterBlobNames, historyForAPI, blobToTitleMapping, sessionId);\n      setChatHistory(prevHistory => [...prevHistory, {\n        query,\n        response: result,\n        timestamp: new Date().toISOString()\n      }]);\n    } catch (err) {\n      setError(err.message || \"An error occurred while processing your query\");\n      console.error(\"Query error:\", err);\n      toast.error(\"Error processing your query\", {\n        toastId: `query-error-${Date.now()}`\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSOPChange = newSelectedSOPs => {\n    setSelectedSOPs(newSelectedSOPs);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chatbot-page\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"chat-container\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-box\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-text\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), chatHistory.length === 0 && !isLoading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"welcome-title\",\n          children: \"Welcome to Regulation & Compliance Assistant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"welcome-subtitle\",\n          children: \"Ask questions about your SOPs and Regulations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SOPDropdown, {\n          sopTitles: allowedTitles,\n          selectedSOPs: selectedSOPs,\n          onSOPChange: handleSOPChange,\n          isLoading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-hints\",\n          children: selectedSOPs.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"filter-indicator\",\n            children: \"\\u2022 Filtering responses to selected documents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), chatHistory.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-history\",\n        children: chatHistory.map((chat, index) => /*#__PURE__*/_jsxDEV(QueryResponse, {\n          query: chat.query,\n          response: chat.response,\n          timestamp: chat.timestamp\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-box\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Processing your query...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(QueryInput, {\n        onSubmit: handleQuery,\n        isLoading: isLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPage, \"cjd0VLqr2UlXVrFEUYBFm8nLOio=\");\n_c = ChatPage;\nexport default ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "useMemo", "Navigation", "QueryInput", "QueryResponse", "SOPDropdown", "queryAPI", "toast", "sopService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "generateUUID", "replace", "c", "r", "Math", "random", "v", "toString", "ChatPage", "_s", "chatHistory", "setChatHistory", "isLoading", "setIsLoading", "error", "setError", "selectedSOPs", "setSelectedSOPs", "sops", "setSops", "loading", "setLoading", "sessionId", "setSessionId", "currentSessionId", "sessionStorage", "setItem", "console", "log", "fetchSOPs", "data", "getAllSOPs", "sops_data", "e", "savedHistory", "getItem", "JSON", "parse", "stringify", "handleRefresh", "removeItem", "window", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "titleToBlobMap", "mapping", "titles", "for<PERSON>ach", "sop", "title", "blob_file_name", "push", "handleQuery", "query", "filterTitles", "length", "filterBlobNames", "map", "filter", "Boolean", "blobToTitleMapping", "Object", "entries", "blobName", "historyForAPI", "turn", "user_query", "bot_answer", "response", "answer", "result", "prevHistory", "timestamp", "Date", "toISOString", "err", "message", "toastId", "now", "handleSOPChange", "newSelectedSOPs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sop<PERSON><PERSON><PERSON>", "onSOPChange", "chat", "index", "size", "onSubmit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/ChatBot/ChatPage.js"], "sourcesContent": ["import { useState, useEffect, useMemo } from \"react\";\r\nimport Navigation from \"../../common/Navigation/Navigation\";\r\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\r\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\r\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\r\nimport { queryAPI } from \"../../../services/queryAPI\";\r\nimport \"./ChatPage.css\";\r\nimport { toast } from \"react-toastify\";\r\nimport sopService from \"../../../services/sopService\";\r\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\r\n\r\n// Generate UUID for session management\r\nconst generateUUID = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n    const r = Math.random() * 16 | 0;\r\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n};\r\n\r\nfunction ChatPage() {\r\n  const [chatHistory, setChatHistory] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\r\n  const [sops, setSops] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [sessionId, setSessionId] = useState(null);\r\n\r\n  // Initialize session ID on component mount\r\n  useEffect(() => {\r\n    // Always generate a new session ID on component mount (page load/refresh)\r\n    // This ensures each browser refresh creates a new chat session\r\n    const currentSessionId = generateUUID();\r\n    sessionStorage.setItem('chatSessionId', currentSessionId);\r\n    setSessionId(currentSessionId);\r\n    console.log('New chat session created on page load:', currentSessionId);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchSOPs = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const data = await sopService.getAllSOPs();\r\n        //console.log(\"Fetched SOPs:\", data);\r\n        setSops(data.sops_data);\r\n      } catch (e) {\r\n        setSops([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchSOPs();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const savedHistory = sessionStorage.getItem('chatHistory');\r\n    if (savedHistory) {\r\n      setChatHistory(JSON.parse(savedHistory));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\r\n  }, [chatHistory]);\r\n\r\n  useEffect(() => {\r\n  const handleRefresh = () => {\r\n    sessionStorage.removeItem('chatHistory');\r\n  };\r\n  window.addEventListener('beforeunload', handleRefresh);\r\n  return () => window.removeEventListener('beforeunload', handleRefresh);\r\n}, []);\r\n\r\n  \r\n\r\n  const { allowedTitles, titleToBlobMap } = useMemo(() => {\r\n    const mapping = {};\r\n    const titles = [];\r\n    sops.forEach((sop) => {\r\n      if (sop.title && sop.blob_file_name) {\r\n        mapping[sop.title] = sop.blob_file_name;\r\n        titles.push(sop.title);\r\n      }\r\n    });\r\n    return { allowedTitles: titles, titleToBlobMap: mapping };\r\n  }, [sops]);\r\n\r\n  const handleQuery = async (query) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const filterTitles =\r\n        selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\r\n\r\n      const filterBlobNames = filterTitles\r\n        .map((title) => titleToBlobMap[title])\r\n        .filter(Boolean);\r\n\r\n      const blobToTitleMapping = {};\r\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\r\n        blobToTitleMapping[blobName] = title;\r\n      });\r\n\r\n      const historyForAPI = chatHistory.map((turn) => ({\r\n        user_query: turn.query,\r\n        bot_answer: turn.response.answer,\r\n      }));\r\n\r\n      const result = await queryAPI(\r\n        query,\r\n        null,\r\n        filterBlobNames,\r\n        historyForAPI,\r\n        blobToTitleMapping,\r\n        sessionId\r\n      );\r\n\r\n      setChatHistory((prevHistory) => [\r\n        ...prevHistory,\r\n        {\r\n          query,\r\n          response: result,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      ]);\r\n    } catch (err) {\r\n      setError(err.message || \"An error occurred while processing your query\");\r\n      console.error(\"Query error:\", err);\r\n      toast.error(\"Error processing your query\", {\r\n        toastId: `query-error-${Date.now()}`,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSOPChange = (newSelectedSOPs) => {\r\n    setSelectedSOPs(newSelectedSOPs);\r\n  };\r\n\r\n  return (\r\n    <div className=\"chatbot-page\">\r\n      <Navigation />\r\n      <main className=\"chat-container\">\r\n        {error && (\r\n          <div className=\"error-box\">\r\n            <p className=\"error-text\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length === 0 && !isLoading && !error && (\r\n          <div className=\"welcome-section\">\r\n            \r\n            <h2 className=\"welcome-title\">\r\n              Welcome to Regulation & Compliance Assistant\r\n            </h2>\r\n            <p className=\"welcome-subtitle\">\r\n              Ask questions about your SOPs and Regulations\r\n            </p>\r\n\r\n            <SOPDropdown\r\n              sopTitles={allowedTitles}\r\n              selectedSOPs={selectedSOPs}\r\n              onSOPChange={handleSOPChange}\r\n              isLoading={loading}\r\n            />\r\n\r\n            <div className=\"welcome-hints\">\r\n              {selectedSOPs.length > 0 && (\r\n                <p className=\"filter-indicator\">\r\n                  • Filtering responses to selected documents\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length > 0 && (\r\n          <div className=\"chat-history\">\r\n            {chatHistory.map((chat, index) => (\r\n              <QueryResponse\r\n                key={index}\r\n                query={chat.query}\r\n                response={chat.response}\r\n                timestamp={chat.timestamp}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {isLoading && (\r\n          <div className=\"loading-box\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <p className=\"loading-text\">Processing your query...</p>\r\n          </div>\r\n        )}\r\n\r\n        <QueryInput onSubmit={handleQuery} isLoading={isLoading} />\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatPage;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACpD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,gBAAgB;AACvB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,cAAc,MAAM,4CAA4C;;AAEvE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;IACzE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAChC,MAAMC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;IACzC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AAED,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd;IACA;IACA,MAAMoC,gBAAgB,GAAGxB,YAAY,CAAC,CAAC;IACvCyB,cAAc,CAACC,OAAO,CAAC,eAAe,EAAEF,gBAAgB,CAAC;IACzDD,YAAY,CAACC,gBAAgB,CAAC;IAC9BG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEJ,gBAAgB,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;EAENpC,SAAS,CAAC,MAAM;IACd,MAAMyC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BR,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMS,IAAI,GAAG,MAAMlC,UAAU,CAACmC,UAAU,CAAC,CAAC;QAC1C;QACAZ,OAAO,CAACW,IAAI,CAACE,SAAS,CAAC;MACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVd,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAENzC,SAAS,CAAC,MAAM;IACd,MAAM8C,YAAY,GAAGT,cAAc,CAACU,OAAO,CAAC,aAAa,CAAC;IAC1D,IAAID,YAAY,EAAE;MAChBvB,cAAc,CAACyB,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN9C,SAAS,CAAC,MAAM;IACdqC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEU,IAAI,CAACE,SAAS,CAAC5B,WAAW,CAAC,CAAC;EACpE,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBtB,SAAS,CAAC,MAAM;IAChB,MAAMmD,aAAa,GAAGA,CAAA,KAAM;MAC1Bd,cAAc,CAACe,UAAU,CAAC,aAAa,CAAC;IAC1C,CAAC;IACDC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEH,aAAa,CAAC;IACtD,OAAO,MAAME,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEJ,aAAa,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAIJ,MAAM;IAAEK,aAAa;IAAEC;EAAe,CAAC,GAAGxD,OAAO,CAAC,MAAM;IACtD,MAAMyD,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,EAAE;IACjB7B,IAAI,CAAC8B,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,cAAc,EAAE;QACnCL,OAAO,CAACG,GAAG,CAACC,KAAK,CAAC,GAAGD,GAAG,CAACE,cAAc;QACvCJ,MAAM,CAACK,IAAI,CAACH,GAAG,CAACC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC;IACF,OAAO;MAAEN,aAAa,EAAEG,MAAM;MAAEF,cAAc,EAAEC;IAAQ,CAAC;EAC3D,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,MAAMmC,WAAW,GAAG,MAAOC,KAAK,IAAK;IACnCzC,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMwC,YAAY,GAChBvC,YAAY,CAACwC,MAAM,GAAG,CAAC,GAAGxC,YAAY,GAAG4B,aAAa;MAExD,MAAMa,eAAe,GAAGF,YAAY,CACjCG,GAAG,CAAER,KAAK,IAAKL,cAAc,CAACK,KAAK,CAAC,CAAC,CACrCS,MAAM,CAACC,OAAO,CAAC;MAElB,MAAMC,kBAAkB,GAAG,CAAC,CAAC;MAC7BC,MAAM,CAACC,OAAO,CAAClB,cAAc,CAAC,CAACG,OAAO,CAAC,CAAC,CAACE,KAAK,EAAEc,QAAQ,CAAC,KAAK;QAC5DH,kBAAkB,CAACG,QAAQ,CAAC,GAAGd,KAAK;MACtC,CAAC,CAAC;MAEF,MAAMe,aAAa,GAAGvD,WAAW,CAACgD,GAAG,CAAEQ,IAAI,KAAM;QAC/CC,UAAU,EAAED,IAAI,CAACZ,KAAK;QACtBc,UAAU,EAAEF,IAAI,CAACG,QAAQ,CAACC;MAC5B,CAAC,CAAC,CAAC;MAEH,MAAMC,MAAM,GAAG,MAAM7E,QAAQ,CAC3B4D,KAAK,EACL,IAAI,EACJG,eAAe,EACfQ,aAAa,EACbJ,kBAAkB,EAClBvC,SACF,CAAC;MAEDX,cAAc,CAAE6D,WAAW,IAAK,CAC9B,GAAGA,WAAW,EACd;QACElB,KAAK;QACLe,QAAQ,EAAEE,MAAM;QAChBE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ7D,QAAQ,CAAC6D,GAAG,CAACC,OAAO,IAAI,+CAA+C,CAAC;MACxElD,OAAO,CAACb,KAAK,CAAC,cAAc,EAAE8D,GAAG,CAAC;MAClCjF,KAAK,CAACmB,KAAK,CAAC,6BAA6B,EAAE;QACzCgE,OAAO,EAAE,eAAeJ,IAAI,CAACK,GAAG,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRlE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmE,eAAe,GAAIC,eAAe,IAAK;IAC3ChE,eAAe,CAACgE,eAAe,CAAC;EAClC,CAAC;EAED,oBACElF,OAAA;IAAKmF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BpF,OAAA,CAACT,UAAU;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdxF,OAAA;MAAMmF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC7BrE,KAAK,iBACJf,OAAA;QAAKmF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpF,OAAA;UAAGmF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAErE;QAAK;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACN,EAEA7E,WAAW,CAAC8C,MAAM,KAAK,CAAC,IAAI,CAAC5C,SAAS,IAAI,CAACE,KAAK,iBAC/Cf,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BpF,OAAA;UAAImF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxF,OAAA;UAAGmF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJxF,OAAA,CAACN,WAAW;UACV+F,SAAS,EAAE5C,aAAc;UACzB5B,YAAY,EAAEA,YAAa;UAC3ByE,WAAW,EAAET,eAAgB;UAC7BpE,SAAS,EAAEQ;QAAQ;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEFxF,OAAA;UAAKmF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BnE,YAAY,CAACwC,MAAM,GAAG,CAAC,iBACtBzD,OAAA;YAAGmF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA7E,WAAW,CAAC8C,MAAM,GAAG,CAAC,iBACrBzD,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BzE,WAAW,CAACgD,GAAG,CAAC,CAACgC,IAAI,EAAEC,KAAK,kBAC3B5F,OAAA,CAACP,aAAa;UAEZ8D,KAAK,EAAEoC,IAAI,CAACpC,KAAM;UAClBe,QAAQ,EAAEqB,IAAI,CAACrB,QAAS;UACxBI,SAAS,EAAEiB,IAAI,CAACjB;QAAU,GAHrBkB,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEA3E,SAAS,iBACRb,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA,CAACF,cAAc;UAAC+F,IAAI,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BxF,OAAA;UAAGmF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN,eAEDxF,OAAA,CAACR,UAAU;QAACsG,QAAQ,EAAExC,WAAY;QAACzC,SAAS,EAAEA;MAAU;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC9E,EAAA,CAvLQD,QAAQ;AAAAsF,EAAA,GAARtF,QAAQ;AAyLjB,eAAeA,QAAQ;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}