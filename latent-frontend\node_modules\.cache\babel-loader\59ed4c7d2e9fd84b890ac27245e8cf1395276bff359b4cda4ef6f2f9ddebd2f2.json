{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\ChatBot\\\\ChatPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useMemo } from \"react\";\nimport Navigation from \"../../common/Navigation/Navigation\";\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\nimport { queryAPI } from \"../../../services/queryAPI\";\nimport \"./ChatPage.css\";\nimport { toast } from \"react-toastify\";\nimport sopService from \"../../../services/sopService\";\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\n\n// Generate UUID for session management\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst generateUUID = () => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\nfunction ChatPage() {\n  _s();\n  const [chatHistory, setChatHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\n  const [sops, setSops] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n\n  // Initialize session ID on component mount\n  useEffect(() => {\n    // Check if session ID exists (for chat persistence across token refreshes)\n    let currentSessionId = sessionStorage.getItem('chatSessionId');\n    if (!currentSessionId) {\n      // Generate new session ID only if none exists\n      currentSessionId = generateUUID();\n      sessionStorage.setItem('chatSessionId', currentSessionId);\n      console.log('New chat session created:', currentSessionId);\n    } else {\n      console.log('Continuing existing chat session:', currentSessionId);\n    }\n    setSessionId(currentSessionId);\n  }, []);\n  useEffect(() => {\n    const fetchSOPs = async () => {\n      setLoading(true);\n      try {\n        const data = await sopService.getAllSOPs();\n        //console.log(\"Fetched SOPs:\", data);\n        setSops(data.sops_data);\n      } catch (e) {\n        setSops([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSOPs();\n  }, []);\n  useEffect(() => {\n    const savedHistory = sessionStorage.getItem('chatHistory');\n    if (savedHistory) {\n      setChatHistory(JSON.parse(savedHistory));\n    }\n  }, []);\n  useEffect(() => {\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\n  }, [chatHistory]);\n\n  // Upload chat session to Azure Blob Storage\n  const uploadChatSession = async (sessionId, chatHistory, trigger = 'unknown') => {\n    if (!chatHistory || chatHistory.length === 0) {\n      console.log('No chat history to upload');\n      return;\n    }\n    try {\n      var _chatHistory$;\n      const uploadData = {\n        session_id: sessionId,\n        chat_history: chatHistory.map(turn => {\n          var _turn$response$source;\n          return {\n            timestamp: turn.timestamp,\n            user_query: turn.query,\n            bot_response: turn.response.answer,\n            sources_count: turn.response.total_sources,\n            filter_titles: ((_turn$response$source = turn.response.sources) === null || _turn$response$source === void 0 ? void 0 : _turn$response$source.map(s => s.doc_name)) || []\n          };\n        }),\n        session_start: (_chatHistory$ = chatHistory[0]) === null || _chatHistory$ === void 0 ? void 0 : _chatHistory$.timestamp,\n        session_end: new Date().toISOString(),\n        total_interactions: chatHistory.length,\n        upload_trigger: trigger\n      };\n      console.log('Uploading chat session:', uploadData);\n\n      // TODO: Replace with actual upload API call\n      // await fetch('/api/chat/upload-session', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(uploadData)\n      // });\n\n      console.log('Chat session upload completed');\n    } catch (error) {\n      console.error('Failed to upload chat session:', error);\n    }\n  };\n\n  // Handle session end events\n  useEffect(() => {\n    const handleBeforeUnload = event => {\n      if (chatHistory.length > 0) {\n        // Use sendBeacon for reliable upload on page unload\n        const uploadData = JSON.stringify({\n          session_id: sessionId,\n          chat_history: chatHistory,\n          upload_trigger: 'browser_close'\n        });\n\n        // TODO: Replace with actual beacon endpoint\n        // navigator.sendBeacon('/api/chat/upload-session-beacon', uploadData);\n        console.log('Would upload session via beacon:', uploadData);\n      }\n    };\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === 'hidden' && chatHistory.length > 0) {\n        // Tab is being hidden (possibly closed)\n        uploadChatSession(sessionId, chatHistory, 'tab_hidden');\n      }\n    };\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n    };\n  }, [sessionId, chatHistory]);\n  const {\n    allowedTitles,\n    titleToBlobMap\n  } = useMemo(() => {\n    const mapping = {};\n    const titles = [];\n    sops.forEach(sop => {\n      if (sop.title && sop.blob_file_name) {\n        mapping[sop.title] = sop.blob_file_name;\n        titles.push(sop.title);\n      }\n    });\n    return {\n      allowedTitles: titles,\n      titleToBlobMap: mapping\n    };\n  }, [sops]);\n  const handleQuery = async query => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const filterTitles = selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\n      const filterBlobNames = filterTitles.map(title => titleToBlobMap[title]).filter(Boolean);\n      const blobToTitleMapping = {};\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\n        blobToTitleMapping[blobName] = title;\n      });\n      const historyForAPI = chatHistory.map(turn => ({\n        user_query: turn.query,\n        bot_answer: turn.response.answer\n      }));\n      const result = await queryAPI(query, null, filterBlobNames, historyForAPI, blobToTitleMapping, sessionId);\n      setChatHistory(prevHistory => [...prevHistory, {\n        query,\n        response: result,\n        timestamp: new Date().toISOString()\n      }]);\n    } catch (err) {\n      setError(err.message || \"An error occurred while processing your query\");\n      console.error(\"Query error:\", err);\n      toast.error(\"Error processing your query\", {\n        toastId: `query-error-${Date.now()}`\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSOPChange = newSelectedSOPs => {\n    setSelectedSOPs(newSelectedSOPs);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chatbot-page\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"chat-container\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-box\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-text\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), chatHistory.length === 0 && !isLoading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"welcome-title\",\n          children: \"Welcome to Regulation & Compliance Assistant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"welcome-subtitle\",\n          children: \"Ask questions about your SOPs and Regulations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SOPDropdown, {\n          sopTitles: allowedTitles,\n          selectedSOPs: selectedSOPs,\n          onSOPChange: handleSOPChange,\n          isLoading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-hints\",\n          children: selectedSOPs.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"filter-indicator\",\n            children: \"\\u2022 Filtering responses to selected documents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), chatHistory.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-history\",\n        children: chatHistory.map((chat, index) => /*#__PURE__*/_jsxDEV(QueryResponse, {\n          query: chat.query,\n          response: chat.response,\n          timestamp: chat.timestamp\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-box\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-text\",\n          children: \"Processing your query...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(QueryInput, {\n        onSubmit: handleQuery,\n        isLoading: isLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPage, \"cjd0VLqr2UlXVrFEUYBFm8nLOio=\");\n_c = ChatPage;\nexport default ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "useMemo", "Navigation", "QueryInput", "QueryResponse", "SOPDropdown", "queryAPI", "toast", "sopService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "generateUUID", "replace", "c", "r", "Math", "random", "v", "toString", "ChatPage", "_s", "chatHistory", "setChatHistory", "isLoading", "setIsLoading", "error", "setError", "selectedSOPs", "setSelectedSOPs", "sops", "setSops", "loading", "setLoading", "sessionId", "setSessionId", "currentSessionId", "sessionStorage", "getItem", "setItem", "console", "log", "fetchSOPs", "data", "getAllSOPs", "sops_data", "e", "savedHistory", "JSON", "parse", "stringify", "uploadChatSession", "trigger", "length", "_chatHistory$", "uploadData", "session_id", "chat_history", "map", "turn", "_turn$response$source", "timestamp", "user_query", "query", "bot_response", "response", "answer", "sources_count", "total_sources", "filter_titles", "sources", "s", "doc_name", "session_start", "session_end", "Date", "toISOString", "total_interactions", "upload_trigger", "handleBeforeUnload", "event", "handleVisibilityChange", "document", "visibilityState", "window", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "titleToBlobMap", "mapping", "titles", "for<PERSON>ach", "sop", "title", "blob_file_name", "push", "handleQuery", "filterTitles", "filterBlobNames", "filter", "Boolean", "blobToTitleMapping", "Object", "entries", "blobName", "historyForAPI", "bot_answer", "result", "prevHistory", "err", "message", "toastId", "now", "handleSOPChange", "newSelectedSOPs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sop<PERSON><PERSON><PERSON>", "onSOPChange", "chat", "index", "size", "onSubmit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/ChatBot/ChatPage.js"], "sourcesContent": ["import { useState, useEffect, useMemo } from \"react\";\r\nimport Navigation from \"../../common/Navigation/Navigation\";\r\nimport QueryInput from \"../../common/QueryInput/QueryInput\";\r\nimport QueryResponse from \"../../common/QueryResponse/QueryResponse\";\r\nimport SOPDropdown from \"../../common/SOPDropdown/SOPDropdown\";\r\nimport { queryAPI } from \"../../../services/queryAPI\";\r\nimport \"./ChatPage.css\";\r\nimport { toast } from \"react-toastify\";\r\nimport sopService from \"../../../services/sopService\";\r\nimport LoadingSpinner from \"../../common/LoadingSpinner/LoadingSpinner\";\r\n\r\n// Generate UUID for session management\r\nconst generateUUID = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n    const r = Math.random() * 16 | 0;\r\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n};\r\n\r\nfunction ChatPage() {\r\n  const [chatHistory, setChatHistory] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [selectedSOPs, setSelectedSOPs] = useState([]);\r\n  const [sops, setSops] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [sessionId, setSessionId] = useState(null);\r\n\r\n  // Initialize session ID on component mount\r\n  useEffect(() => {\r\n    // Check if session ID exists (for chat persistence across token refreshes)\r\n    let currentSessionId = sessionStorage.getItem('chatSessionId');\r\n\r\n    if (!currentSessionId) {\r\n      // Generate new session ID only if none exists\r\n      currentSessionId = generateUUID();\r\n      sessionStorage.setItem('chatSessionId', currentSessionId);\r\n      console.log('New chat session created:', currentSessionId);\r\n    } else {\r\n      console.log('Continuing existing chat session:', currentSessionId);\r\n    }\r\n\r\n    setSessionId(currentSessionId);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchSOPs = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const data = await sopService.getAllSOPs();\r\n        //console.log(\"Fetched SOPs:\", data);\r\n        setSops(data.sops_data);\r\n      } catch (e) {\r\n        setSops([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchSOPs();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const savedHistory = sessionStorage.getItem('chatHistory');\r\n    if (savedHistory) {\r\n      setChatHistory(JSON.parse(savedHistory));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));\r\n  }, [chatHistory]);\r\n\r\n  // Upload chat session to Azure Blob Storage\r\n  const uploadChatSession = async (sessionId, chatHistory, trigger = 'unknown') => {\r\n    if (!chatHistory || chatHistory.length === 0) {\r\n      console.log('No chat history to upload');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const uploadData = {\r\n        session_id: sessionId,\r\n        chat_history: chatHistory.map(turn => ({\r\n          timestamp: turn.timestamp,\r\n          user_query: turn.query,\r\n          bot_response: turn.response.answer,\r\n          sources_count: turn.response.total_sources,\r\n          filter_titles: turn.response.sources?.map(s => s.doc_name) || []\r\n        })),\r\n        session_start: chatHistory[0]?.timestamp,\r\n        session_end: new Date().toISOString(),\r\n        total_interactions: chatHistory.length,\r\n        upload_trigger: trigger\r\n      };\r\n\r\n      console.log('Uploading chat session:', uploadData);\r\n\r\n      // TODO: Replace with actual upload API call\r\n      // await fetch('/api/chat/upload-session', {\r\n      //   method: 'POST',\r\n      //   headers: { 'Content-Type': 'application/json' },\r\n      //   body: JSON.stringify(uploadData)\r\n      // });\r\n\r\n      console.log('Chat session upload completed');\r\n    } catch (error) {\r\n      console.error('Failed to upload chat session:', error);\r\n    }\r\n  };\r\n\r\n  // Handle session end events\r\n  useEffect(() => {\r\n    const handleBeforeUnload = (event) => {\r\n      if (chatHistory.length > 0) {\r\n        // Use sendBeacon for reliable upload on page unload\r\n        const uploadData = JSON.stringify({\r\n          session_id: sessionId,\r\n          chat_history: chatHistory,\r\n          upload_trigger: 'browser_close'\r\n        });\r\n\r\n        // TODO: Replace with actual beacon endpoint\r\n        // navigator.sendBeacon('/api/chat/upload-session-beacon', uploadData);\r\n        console.log('Would upload session via beacon:', uploadData);\r\n      }\r\n    };\r\n\r\n    const handleVisibilityChange = () => {\r\n      if (document.visibilityState === 'hidden' && chatHistory.length > 0) {\r\n        // Tab is being hidden (possibly closed)\r\n        uploadChatSession(sessionId, chatHistory, 'tab_hidden');\r\n      }\r\n    };\r\n\r\n    window.addEventListener('beforeunload', handleBeforeUnload);\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleBeforeUnload);\r\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n    };\r\n  }, [sessionId, chatHistory]);\r\n\r\n\r\n  \r\n\r\n  const { allowedTitles, titleToBlobMap } = useMemo(() => {\r\n    const mapping = {};\r\n    const titles = [];\r\n    sops.forEach((sop) => {\r\n      if (sop.title && sop.blob_file_name) {\r\n        mapping[sop.title] = sop.blob_file_name;\r\n        titles.push(sop.title);\r\n      }\r\n    });\r\n    return { allowedTitles: titles, titleToBlobMap: mapping };\r\n  }, [sops]);\r\n\r\n  const handleQuery = async (query) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const filterTitles =\r\n        selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;\r\n\r\n      const filterBlobNames = filterTitles\r\n        .map((title) => titleToBlobMap[title])\r\n        .filter(Boolean);\r\n\r\n      const blobToTitleMapping = {};\r\n      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {\r\n        blobToTitleMapping[blobName] = title;\r\n      });\r\n\r\n      const historyForAPI = chatHistory.map((turn) => ({\r\n        user_query: turn.query,\r\n        bot_answer: turn.response.answer,\r\n      }));\r\n\r\n      const result = await queryAPI(\r\n        query,\r\n        null,\r\n        filterBlobNames,\r\n        historyForAPI,\r\n        blobToTitleMapping,\r\n        sessionId\r\n      );\r\n\r\n      setChatHistory((prevHistory) => [\r\n        ...prevHistory,\r\n        {\r\n          query,\r\n          response: result,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      ]);\r\n    } catch (err) {\r\n      setError(err.message || \"An error occurred while processing your query\");\r\n      console.error(\"Query error:\", err);\r\n      toast.error(\"Error processing your query\", {\r\n        toastId: `query-error-${Date.now()}`,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSOPChange = (newSelectedSOPs) => {\r\n    setSelectedSOPs(newSelectedSOPs);\r\n  };\r\n\r\n  return (\r\n    <div className=\"chatbot-page\">\r\n      <Navigation />\r\n      <main className=\"chat-container\">\r\n        {error && (\r\n          <div className=\"error-box\">\r\n            <p className=\"error-text\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length === 0 && !isLoading && !error && (\r\n          <div className=\"welcome-section\">\r\n            \r\n            <h2 className=\"welcome-title\">\r\n              Welcome to Regulation & Compliance Assistant\r\n            </h2>\r\n            <p className=\"welcome-subtitle\">\r\n              Ask questions about your SOPs and Regulations\r\n            </p>\r\n\r\n            <SOPDropdown\r\n              sopTitles={allowedTitles}\r\n              selectedSOPs={selectedSOPs}\r\n              onSOPChange={handleSOPChange}\r\n              isLoading={loading}\r\n            />\r\n\r\n            <div className=\"welcome-hints\">\r\n              {selectedSOPs.length > 0 && (\r\n                <p className=\"filter-indicator\">\r\n                  • Filtering responses to selected documents\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {chatHistory.length > 0 && (\r\n          <div className=\"chat-history\">\r\n            {chatHistory.map((chat, index) => (\r\n              <QueryResponse\r\n                key={index}\r\n                query={chat.query}\r\n                response={chat.response}\r\n                timestamp={chat.timestamp}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {isLoading && (\r\n          <div className=\"loading-box\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <p className=\"loading-text\">Processing your query...</p>\r\n          </div>\r\n        )}\r\n\r\n        <QueryInput onSubmit={handleQuery} isLoading={isLoading} />\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatPage;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACpD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,gBAAgB;AACvB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,cAAc,MAAM,4CAA4C;;AAEvE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;IACzE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAChC,MAAMC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;IACzC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AAED,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd;IACA,IAAIoC,gBAAgB,GAAGC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;IAE9D,IAAI,CAACF,gBAAgB,EAAE;MACrB;MACAA,gBAAgB,GAAGxB,YAAY,CAAC,CAAC;MACjCyB,cAAc,CAACE,OAAO,CAAC,eAAe,EAAEH,gBAAgB,CAAC;MACzDI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEL,gBAAgB,CAAC;IAC5D,CAAC,MAAM;MACLI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEL,gBAAgB,CAAC;IACpE;IAEAD,YAAY,CAACC,gBAAgB,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAENpC,SAAS,CAAC,MAAM;IACd,MAAM0C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BT,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMU,IAAI,GAAG,MAAMnC,UAAU,CAACoC,UAAU,CAAC,CAAC;QAC1C;QACAb,OAAO,CAACY,IAAI,CAACE,SAAS,CAAC;MACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVf,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDS,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN1C,SAAS,CAAC,MAAM;IACd,MAAM+C,YAAY,GAAGV,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;IAC1D,IAAIS,YAAY,EAAE;MAChBxB,cAAc,CAACyB,IAAI,CAACC,KAAK,CAACF,YAAY,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN/C,SAAS,CAAC,MAAM;IACdqC,cAAc,CAACE,OAAO,CAAC,aAAa,EAAES,IAAI,CAACE,SAAS,CAAC5B,WAAW,CAAC,CAAC;EACpE,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM6B,iBAAiB,GAAG,MAAAA,CAAOjB,SAAS,EAAEZ,WAAW,EAAE8B,OAAO,GAAG,SAAS,KAAK;IAC/E,IAAI,CAAC9B,WAAW,IAAIA,WAAW,CAAC+B,MAAM,KAAK,CAAC,EAAE;MAC5Cb,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC;IACF;IAEA,IAAI;MAAA,IAAAa,aAAA;MACF,MAAMC,UAAU,GAAG;QACjBC,UAAU,EAAEtB,SAAS;QACrBuB,YAAY,EAAEnC,WAAW,CAACoC,GAAG,CAACC,IAAI;UAAA,IAAAC,qBAAA;UAAA,OAAK;YACrCC,SAAS,EAAEF,IAAI,CAACE,SAAS;YACzBC,UAAU,EAAEH,IAAI,CAACI,KAAK;YACtBC,YAAY,EAAEL,IAAI,CAACM,QAAQ,CAACC,MAAM;YAClCC,aAAa,EAAER,IAAI,CAACM,QAAQ,CAACG,aAAa;YAC1CC,aAAa,EAAE,EAAAT,qBAAA,GAAAD,IAAI,CAACM,QAAQ,CAACK,OAAO,cAAAV,qBAAA,uBAArBA,qBAAA,CAAuBF,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,KAAI;UAChE,CAAC;QAAA,CAAC,CAAC;QACHC,aAAa,GAAAnB,aAAA,GAAEhC,WAAW,CAAC,CAAC,CAAC,cAAAgC,aAAA,uBAAdA,aAAA,CAAgBO,SAAS;QACxCa,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCC,kBAAkB,EAAEvD,WAAW,CAAC+B,MAAM;QACtCyB,cAAc,EAAE1B;MAClB,CAAC;MAEDZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEc,UAAU,CAAC;;MAElD;MACA;MACA;MACA;MACA;MACA;;MAEAf,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA1B,SAAS,CAAC,MAAM;IACd,MAAM+E,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI1D,WAAW,CAAC+B,MAAM,GAAG,CAAC,EAAE;QAC1B;QACA,MAAME,UAAU,GAAGP,IAAI,CAACE,SAAS,CAAC;UAChCM,UAAU,EAAEtB,SAAS;UACrBuB,YAAY,EAAEnC,WAAW;UACzBwD,cAAc,EAAE;QAClB,CAAC,CAAC;;QAEF;QACA;QACAtC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEc,UAAU,CAAC;MAC7D;IACF,CAAC;IAED,MAAM0B,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIC,QAAQ,CAACC,eAAe,KAAK,QAAQ,IAAI7D,WAAW,CAAC+B,MAAM,GAAG,CAAC,EAAE;QACnE;QACAF,iBAAiB,CAACjB,SAAS,EAAEZ,WAAW,EAAE,YAAY,CAAC;MACzD;IACF,CAAC;IAED8D,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEN,kBAAkB,CAAC;IAC3DG,QAAQ,CAACG,gBAAgB,CAAC,kBAAkB,EAAEJ,sBAAsB,CAAC;IAErE,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEP,kBAAkB,CAAC;MAC9DG,QAAQ,CAACI,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,CAAC/C,SAAS,EAAEZ,WAAW,CAAC,CAAC;EAK5B,MAAM;IAAEiE,aAAa;IAAEC;EAAe,CAAC,GAAGvF,OAAO,CAAC,MAAM;IACtD,MAAMwF,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,EAAE;IACjB5D,IAAI,CAAC6D,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,cAAc,EAAE;QACnCL,OAAO,CAACG,GAAG,CAACC,KAAK,CAAC,GAAGD,GAAG,CAACE,cAAc;QACvCJ,MAAM,CAACK,IAAI,CAACH,GAAG,CAACC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC;IACF,OAAO;MAAEN,aAAa,EAAEG,MAAM;MAAEF,cAAc,EAAEC;IAAQ,CAAC;EAC3D,CAAC,EAAE,CAAC3D,IAAI,CAAC,CAAC;EAEV,MAAMkE,WAAW,GAAG,MAAOjC,KAAK,IAAK;IACnCtC,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMsE,YAAY,GAChBrE,YAAY,CAACyB,MAAM,GAAG,CAAC,GAAGzB,YAAY,GAAG2D,aAAa;MAExD,MAAMW,eAAe,GAAGD,YAAY,CACjCvC,GAAG,CAAEmC,KAAK,IAAKL,cAAc,CAACK,KAAK,CAAC,CAAC,CACrCM,MAAM,CAACC,OAAO,CAAC;MAElB,MAAMC,kBAAkB,GAAG,CAAC,CAAC;MAC7BC,MAAM,CAACC,OAAO,CAACf,cAAc,CAAC,CAACG,OAAO,CAAC,CAAC,CAACE,KAAK,EAAEW,QAAQ,CAAC,KAAK;QAC5DH,kBAAkB,CAACG,QAAQ,CAAC,GAAGX,KAAK;MACtC,CAAC,CAAC;MAEF,MAAMY,aAAa,GAAGnF,WAAW,CAACoC,GAAG,CAAEC,IAAI,KAAM;QAC/CG,UAAU,EAAEH,IAAI,CAACI,KAAK;QACtB2C,UAAU,EAAE/C,IAAI,CAACM,QAAQ,CAACC;MAC5B,CAAC,CAAC,CAAC;MAEH,MAAMyC,MAAM,GAAG,MAAMrG,QAAQ,CAC3ByD,KAAK,EACL,IAAI,EACJmC,eAAe,EACfO,aAAa,EACbJ,kBAAkB,EAClBnE,SACF,CAAC;MAEDX,cAAc,CAAEqF,WAAW,IAAK,CAC9B,GAAGA,WAAW,EACd;QACE7C,KAAK;QACLE,QAAQ,EAAE0C,MAAM;QAChB9C,SAAS,EAAE,IAAIc,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZlF,QAAQ,CAACkF,GAAG,CAACC,OAAO,IAAI,+CAA+C,CAAC;MACxEtE,OAAO,CAACd,KAAK,CAAC,cAAc,EAAEmF,GAAG,CAAC;MAClCtG,KAAK,CAACmB,KAAK,CAAC,6BAA6B,EAAE;QACzCqF,OAAO,EAAE,eAAepC,IAAI,CAACqC,GAAG,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRvF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwF,eAAe,GAAIC,eAAe,IAAK;IAC3CrF,eAAe,CAACqF,eAAe,CAAC;EAClC,CAAC;EAED,oBACEvG,OAAA;IAAKwG,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BzG,OAAA,CAACT,UAAU;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACd7G,OAAA;MAAMwG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC7B1F,KAAK,iBACJf,OAAA;QAAKwG,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBzG,OAAA;UAAGwG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAE1F;QAAK;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACN,EAEAlG,WAAW,CAAC+B,MAAM,KAAK,CAAC,IAAI,CAAC7B,SAAS,IAAI,CAACE,KAAK,iBAC/Cf,OAAA;QAAKwG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BzG,OAAA;UAAIwG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7G,OAAA;UAAGwG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ7G,OAAA,CAACN,WAAW;UACVoH,SAAS,EAAElC,aAAc;UACzB3D,YAAY,EAAEA,YAAa;UAC3B8F,WAAW,EAAET,eAAgB;UAC7BzF,SAAS,EAAEQ;QAAQ;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEF7G,OAAA;UAAKwG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BxF,YAAY,CAACyB,MAAM,GAAG,CAAC,iBACtB1C,OAAA;YAAGwG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAlG,WAAW,CAAC+B,MAAM,GAAG,CAAC,iBACrB1C,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B9F,WAAW,CAACoC,GAAG,CAAC,CAACiE,IAAI,EAAEC,KAAK,kBAC3BjH,OAAA,CAACP,aAAa;UAEZ2D,KAAK,EAAE4D,IAAI,CAAC5D,KAAM;UAClBE,QAAQ,EAAE0D,IAAI,CAAC1D,QAAS;UACxBJ,SAAS,EAAE8D,IAAI,CAAC9D;QAAU,GAHrB+D,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEAhG,SAAS,iBACRb,OAAA;QAAKwG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzG,OAAA,CAACF,cAAc;UAACoH,IAAI,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/B7G,OAAA;UAAGwG,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN,eAED7G,OAAA,CAACR,UAAU;QAAC2H,QAAQ,EAAE9B,WAAY;QAACxE,SAAS,EAAEA;MAAU;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACnG,EAAA,CA9PQD,QAAQ;AAAA2G,EAAA,GAAR3G,QAAQ;AAgQjB,eAAeA,QAAQ;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}