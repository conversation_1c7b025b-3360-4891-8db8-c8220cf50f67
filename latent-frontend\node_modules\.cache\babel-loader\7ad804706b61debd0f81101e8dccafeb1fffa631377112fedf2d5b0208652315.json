{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}], [\"path\", {\n  d: \"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2\",\n  key: \"80xlxr\"\n}], [\"path\", {\n  d: \"M5 10v2a7 7 0 0 0 12 5\",\n  key: \"p2k8kg\"\n}], [\"path\", {\n  d: \"M15 9.34V5a3 3 0 0 0-5.68-1.33\",\n  key: \"1gzdoj\"\n}], [\"path\", {\n  d: \"M9 9v3a3 3 0 0 0 5.12 2.12\",\n  key: \"r2i35w\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"19\",\n  y2: \"22\",\n  key: \"x3vr5v\"\n}]];\nconst MicOff = createLucideIcon(\"mic-off\", __iconNode);\nexport { __iconNode, MicOff as default };", "map": {"version": 3, "names": ["__iconNode", "x1", "x2", "y1", "y2", "key", "d", "<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\node_modules\\lucide-react\\src\\icons\\mic-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n  ['path', { d: 'M18.89 13.23A7.12 7.12 0 0 0 19 12v-2', key: '80xlxr' }],\n  ['path', { d: 'M5 10v2a7 7 0 0 0 12 5', key: 'p2k8kg' }],\n  ['path', { d: 'M15 9.34V5a3 3 0 0 0-5.68-1.33', key: '1gzdoj' }],\n  ['path', { d: 'M9 9v3a3 3 0 0 0 5.12 2.12', key: 'r2i35w' }],\n  ['line', { x1: '12', x2: '12', y1: '19', y2: '22', key: 'x3vr5v' }],\n];\n\n/**\n * @component @name MicOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMiIgeDI9IjIyIiB5MT0iMiIgeTI9IjIyIiAvPgogIDxwYXRoIGQ9Ik0xOC44OSAxMy4yM0E3LjEyIDcuMTIgMCAwIDAgMTkgMTJ2LTIiIC8+CiAgPHBhdGggZD0iTTUgMTB2MmE3IDcgMCAwIDAgMTIgNSIgLz4KICA8cGF0aCBkPSJNMTUgOS4zNFY1YTMgMyAwIDAgMC01LjY4LTEuMzMiIC8+CiAgPHBhdGggZD0iTTkgOXYzYTMgMyAwIDAgMCA1LjEyIDIuMTIiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIxOSIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mic-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MicOff = createLucideIcon('mic-off', __iconNode);\n\nexport default MicOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAU,GACpE;AAaM,MAAAE,MAAA,GAASC,gBAAiB,YAAWR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}