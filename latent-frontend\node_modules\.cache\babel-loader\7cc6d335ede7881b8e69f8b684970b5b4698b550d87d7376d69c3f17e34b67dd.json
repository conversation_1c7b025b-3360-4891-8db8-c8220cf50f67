{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v.343\",\n  key: \"1gyhex\"\n}], [\"path\", {\n  d: \"M18.218 18.218A7 7 0 0 1 5 15V9a7 7 0 0 1 .782-3.218\",\n  key: \"ukzz01\"\n}], [\"path\", {\n  d: \"M19 13.343V9A7 7 0 0 0 8.56 2.902\",\n  key: \"104jy9\"\n}], [\"path\", {\n  d: \"M22 22 2 2\",\n  key: \"1r8tn9\"\n}]];\nconst MouseOff = createLucideIcon(\"mouse-off\", __iconNode);\nexport { __iconNode, MouseOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\node_modules\\lucide-react\\src\\icons\\mouse-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v.343', key: '1gyhex' }],\n  ['path', { d: 'M18.218 18.218A7 7 0 0 1 5 15V9a7 7 0 0 1 .782-3.218', key: 'ukzz01' }],\n  ['path', { d: 'M19 13.343V9A7 7 0 0 0 8.56 2.902', key: '104jy9' }],\n  ['path', { d: 'M22 22 2 2', key: '1r8tn9' }],\n];\n\n/**\n * @component @name MouseOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnYuMzQzIiAvPgogIDxwYXRoIGQ9Ik0xOC4yMTggMTguMjE4QTcgNyAwIDAgMSA1IDE1VjlhNyA3IDAgMCAxIC43ODItMy4yMTgiIC8+CiAgPHBhdGggZD0iTTE5IDEzLjM0M1Y5QTcgNyAwIDAgMCA4LjU2IDIuOTAyIiAvPgogIDxwYXRoIGQ9Ik0yMiAyMiAyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/mouse-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MouseOff = createLucideIcon('mouse-off', __iconNode);\n\nexport default MouseOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,sDAAwD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrF,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}