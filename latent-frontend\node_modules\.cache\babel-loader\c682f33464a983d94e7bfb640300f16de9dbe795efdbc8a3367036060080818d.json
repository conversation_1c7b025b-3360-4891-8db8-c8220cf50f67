{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 17 2 2 4-4\",\n  key: \"1jhpwq\"\n}], [\"path\", {\n  d: \"m3 7 2 2 4-4\",\n  key: \"1obspn\"\n}], [\"path\", {\n  d: \"M13 6h8\",\n  key: \"15sg57\"\n}], [\"path\", {\n  d: \"M13 12h8\",\n  key: \"h98zly\"\n}], [\"path\", {\n  d: \"M13 18h8\",\n  key: \"oe0vm4\"\n}]];\nconst ListChecks = createLucideIcon(\"list-checks\", __iconNode);\nexport { __iconNode, ListChecks as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ListChecks", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\node_modules\\lucide-react\\src\\icons\\list-checks.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm3 17 2 2 4-4', key: '1jhpwq' }],\n  ['path', { d: 'm3 7 2 2 4-4', key: '1obspn' }],\n  ['path', { d: 'M13 6h8', key: '15sg57' }],\n  ['path', { d: 'M13 12h8', key: 'h98zly' }],\n  ['path', { d: 'M13 18h8', key: 'oe0vm4' }],\n];\n\n/**\n * @component @name ListChecks\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxNyAyIDIgNC00IiAvPgogIDxwYXRoIGQ9Im0zIDcgMiAyIDQtNCIgLz4KICA8cGF0aCBkPSJNMTMgNmg4IiAvPgogIDxwYXRoIGQ9Ik0xMyAxMmg4IiAvPgogIDxwYXRoIGQ9Ik0xMyAxOGg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/list-checks\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListChecks = createLucideIcon('list-checks', __iconNode);\n\nexport default ListChecks;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}