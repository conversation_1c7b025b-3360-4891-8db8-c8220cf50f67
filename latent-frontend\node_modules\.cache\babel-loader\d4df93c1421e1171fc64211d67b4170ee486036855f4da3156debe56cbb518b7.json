{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12H3\",\n  key: \"1a2rj7\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M10 18H3\",\n  key: \"13769t\"\n}], [\"path\", {\n  d: \"M21 6v10a2 2 0 0 1-2 2h-5\",\n  key: \"ilrcs8\"\n}], [\"path\", {\n  d: \"m16 16-2 2 2 2\",\n  key: \"kkc6pm\"\n}]];\nconst ListEnd = createLucideIcon(\"list-end\", __iconNode);\nexport { __iconNode, ListEnd as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ListEnd", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\zipp\\latent-frontend\\node_modules\\lucide-react\\src\\icons\\list-end.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 12H3', key: '1a2rj7' }],\n  ['path', { d: 'M16 6H3', key: '1wxfjs' }],\n  ['path', { d: 'M10 18H3', key: '13769t' }],\n  ['path', { d: 'M21 6v10a2 2 0 0 1-2 2h-5', key: 'ilrcs8' }],\n  ['path', { d: 'm16 16-2 2 2 2', key: 'kkc6pm' }],\n];\n\n/**\n * @component @name ListEnd\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTJIMyIgLz4KICA8cGF0aCBkPSJNMTYgNkgzIiAvPgogIDxwYXRoIGQ9Ik0xMCAxOEgzIiAvPgogIDxwYXRoIGQ9Ik0yMSA2djEwYTIgMiAwIDAgMS0yIDJoLTUiIC8+CiAgPHBhdGggZD0ibTE2IDE2LTIgMiAyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list-end\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListEnd = createLucideIcon('list-end', __iconNode);\n\nexport default ListEnd;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAU,GACjD;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}