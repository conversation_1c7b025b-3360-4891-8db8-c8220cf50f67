import { useState, useEffect, useMemo } from "react";
import Navigation from "../../common/Navigation/Navigation";
import QueryInput from "../../common/QueryInput/QueryInput";
import QueryResponse from "../../common/QueryResponse/QueryResponse";
import SOPDropdown from "../../common/SOPDropdown/SOPDropdown";
import { queryAPI } from "../../../services/queryAPI";
import "./ChatPage.css";
import { toast } from "react-toastify";
import sopService from "../../../services/sopService";
import LoadingSpinner from "../../common/LoadingSpinner/LoadingSpinner";

// Generate UUID for session management
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

function ChatPage() {
  const [chatHistory, setChatHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedSOPs, setSelectedSOPs] = useState([]);
  const [sops, setSops] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);

  // Initialize session ID on component mount
  useEffect(() => {
    // Generate a new session ID every time the ChatPage component mounts
    // This ensures each visit to the chat page (including refresh) creates a new session
    const currentSessionId = generateUUID();
    setSessionId(currentSessionId);
    console.log('New chat session created:', currentSessionId);

    // Optional: Clear any existing session data on new session
    sessionStorage.removeItem('chatSessionId');
    sessionStorage.setItem('chatSessionId', currentSessionId);
  }, []);

  useEffect(() => {
    const fetchSOPs = async () => {
      setLoading(true);
      try {
        const data = await sopService.getAllSOPs();
        //console.log("Fetched SOPs:", data);
        setSops(data.sops_data);
      } catch (e) {
        setSops([]);
      } finally {
        setLoading(false);
      }
    };
    fetchSOPs();
  }, []);

  useEffect(() => {
    const savedHistory = sessionStorage.getItem('chatHistory');
    if (savedHistory) {
      setChatHistory(JSON.parse(savedHistory));
    }
  }, []);

  useEffect(() => {
    sessionStorage.setItem('chatHistory', JSON.stringify(chatHistory));
  }, [chatHistory]);


  

  const { allowedTitles, titleToBlobMap } = useMemo(() => {
    const mapping = {};
    const titles = [];
    sops.forEach((sop) => {
      if (sop.title && sop.blob_file_name) {
        mapping[sop.title] = sop.blob_file_name;
        titles.push(sop.title);
      }
    });
    return { allowedTitles: titles, titleToBlobMap: mapping };
  }, [sops]);

  const handleQuery = async (query) => {
    setIsLoading(true);
    setError(null);

    try {
      const filterTitles =
        selectedSOPs.length > 0 ? selectedSOPs : allowedTitles;

      const filterBlobNames = filterTitles
        .map((title) => titleToBlobMap[title])
        .filter(Boolean);

      const blobToTitleMapping = {};
      Object.entries(titleToBlobMap).forEach(([title, blobName]) => {
        blobToTitleMapping[blobName] = title;
      });

      const historyForAPI = chatHistory.map((turn) => ({
        user_query: turn.query,
        bot_answer: turn.response.answer,
      }));

      const result = await queryAPI(
        query,
        null,
        filterBlobNames,
        historyForAPI,
        blobToTitleMapping,
        sessionId
      );

      setChatHistory((prevHistory) => [
        ...prevHistory,
        {
          query,
          response: result,
          timestamp: new Date().toISOString(),
        },
      ]);
    } catch (err) {
      setError(err.message || "An error occurred while processing your query");
      console.error("Query error:", err);
      toast.error("Error processing your query", {
        toastId: `query-error-${Date.now()}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSOPChange = (newSelectedSOPs) => {
    setSelectedSOPs(newSelectedSOPs);
  };

  return (
    <div className="chatbot-page">
      <Navigation />
      <main className="chat-container">
        {error && (
          <div className="error-box">
            <p className="error-text">{error}</p>
          </div>
        )}

        {chatHistory.length === 0 && !isLoading && !error && (
          <div className="welcome-section">
            
            <h2 className="welcome-title">
              Welcome to Regulation & Compliance Assistant
            </h2>
            <p className="welcome-subtitle">
              Ask questions about your SOPs and Regulations
            </p>

            <SOPDropdown
              sopTitles={allowedTitles}
              selectedSOPs={selectedSOPs}
              onSOPChange={handleSOPChange}
              isLoading={loading}
            />

            <div className="welcome-hints">
              {selectedSOPs.length > 0 && (
                <p className="filter-indicator">
                  • Filtering responses to selected documents
                </p>
              )}
            </div>
          </div>
        )}

        {chatHistory.length > 0 && (
          <div className="chat-history">
            {chatHistory.map((chat, index) => (
              <QueryResponse
                key={index}
                query={chat.query}
                response={chat.response}
                timestamp={chat.timestamp}
              />
            ))}
          </div>
        )}

        {isLoading && (
          <div className="loading-box">
            <LoadingSpinner size="large" />
            <p className="loading-text">Processing your query...</p>
          </div>
        )}

        <QueryInput onSubmit={handleQuery} isLoading={isLoading} />
      </main>
    </div>
  );
}

export default ChatPage;