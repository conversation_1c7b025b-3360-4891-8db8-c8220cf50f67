import API_URLS from "../config/apiUrls";
import apiService from "./api";

export const queryAPI = async (
  query,
  userId = null,
  filterTitles = null,
  chatHistory = [],
  blobToTitleMapping = null,
  sessionId = null
) => {
  try {
    const requestBody = {
      query,
      user_id: userId,
      filter_titles: [],
      chat_history: [],
      session_id: sessionId,
    };

    // Add filter titles if provided
    if (filterTitles && filterTitles.length > 0) {
      requestBody.filter_titles = filterTitles;
    }

    // Add chat history if provided
    if (chatHistory && chatHistory.length > 0) {
      requestBody.chat_history = chatHistory;
    }

    // Add blob to title mapping if provided
    if (blobToTitleMapping) {
      requestBody.blob_to_title_mapping = blobToTitleMapping;
    }

    //console.log("Making request to:", API_URLS.CHAT.QUERY);
    //console.log("With body:", requestBody);
    const response = await apiService.post(API_URLS.CHAT.QUERY, requestBody);
    //console.log("Got response:", response);
    return response;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
};