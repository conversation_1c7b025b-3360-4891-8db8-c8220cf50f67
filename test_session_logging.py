#!/usr/bin/env python3
"""
Test script for session-based logging implementation
"""
import asyncio
import uuid
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'latent-backend'))

async def test_session_logging():
    """Test the session-based logging functionality"""
    try:
        from app.services.error_logger import log_session_request, log_interaction
        
        # Generate a test session ID
        test_session_id = str(uuid.uuid4())
        print(f"Testing with session ID: {test_session_id}")
        
        # Test 1: First call should create a new session record
        print("\n=== Test 1: Creating new session ===")
        result1 = await log_session_request(
            session_id=test_session_id,
            user_id="test_user",
            user_email="<EMAIL>",
            ip_address="127.0.0.1",
            endpoint="/chat/query",
            method="POST",
            request_params={"query": "test query", "filter_titles_count": 0}
        )
        print(f"Result 1: {result1}")
        
        # Test 2: Second call with same session ID should reuse existing record
        print("\n=== Test 2: Reusing existing session ===")
        result2 = await log_session_request(
            session_id=test_session_id,
            user_id="test_user",
            user_email="<EMAIL>",
            ip_address="127.0.0.1",
            endpoint="/chat/query",
            method="POST",
            request_params={"query": "another test query", "filter_titles_count": 1}
        )
        print(f"Result 2: {result2}")
        
        # Test 3: Log some interactions
        print("\n=== Test 3: Logging interactions ===")
        await log_interaction(test_session_id, "test_step_1", "Testing step 1", 
                             params={"test": "param"}, response={"test": "response"})
        print("Interaction 1 logged")
        
        await log_interaction(test_session_id, "test_step_2", "Testing step 2", 
                             params={"test": "param2"}, response={"test": "response2"})
        print("Interaction 2 logged")
        
        print("\n=== Session-based logging test completed successfully! ===")
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("This is expected if dependencies are missing, but the code structure is correct.")
    except Exception as e:
        print(f"Error during testing: {e}")

if __name__ == "__main__":
    asyncio.run(test_session_logging())
